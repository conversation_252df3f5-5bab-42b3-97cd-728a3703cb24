{"version": 3, "names": ["_core", "require", "_traverse", "_helperReplaceSupers", "_helperMemberExpressionToFunctions", "_helperOptimiseCallExpression", "_helperAnnotateAsPure", "_helperSkipTransparentExpressionWrappers", "ts", "newHelpers", "file", "availableHelper", "buildPrivateNamesMap", "className", "privateFieldsAsSymbolsOrProperties", "props", "privateNamesMap", "Map", "classBrandId", "prop", "isPrivate", "name", "node", "key", "id", "update", "get", "isMethod", "isProperty", "isStatic", "static", "initAdded", "scope", "generateUidIdentifier", "method", "set", "isClassPrivateMethod", "kind", "body", "$", "length", "t", "isReturnStatement", "isCallExpression", "argument", "arguments", "isThisExpression", "isIdentifier", "callee", "getId", "cloneNode", "getterDeclared", "params", "isExpressionStatement", "expression", "setId", "setter<PERSON><PERSON><PERSON>ed", "methodId", "buildPrivateNamesNodes", "privateFieldsAsProperties", "privateFieldsAsSymbols", "state", "initNodes", "injectedIds", "Set", "value", "isGetterOrSetter", "init", "callExpression", "addHelper", "stringLiteral", "identifier", "has", "add", "newExpression", "annotateAsPure", "push", "template", "statement", "ast", "privateNameVisitorFactory", "visitor", "nestedVisitor", "visitors", "environmentVisitor", "Object", "assign", "privateNameVisitor", "Class", "path", "visiblePrivateNames", "redeclared", "delete", "traverse", "<PERSON><PERSON><PERSON>", "PrivateName", "noDocumentAll", "parentPath", "isMemberExpression", "property", "isOptionalMemberExpression", "includes", "handle", "unshadow", "innerBinding", "_scope", "hasBinding", "bindingIdentifierEquals", "rename", "parent", "buildCheckInRHS", "rhs", "inRHSIsObject", "privateInVisitor", "BinaryExpression", "operator", "left", "right", "isPrivateName", "classRef", "replaceWith", "readOnlyError", "writeOnlyError", "console", "warn", "buildUndefinedNode", "buildStaticPrivateFieldAccess", "expr", "noUninitializedPrivateFieldAccess", "memberExpression", "autoInherits", "fn", "member", "inherits", "apply", "privateNameHandlerSpec", "memoise", "count", "object", "memo", "maybeGenerateMemoised", "memoiser", "receiver", "privateName", "cloneId", "helper<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "err", "sequenceExpression", "boundGet", "assignmentExpression", "destructureSet", "helper", "_unused", "Error", "getCall", "ref", "pop", "setCall", "buildCodeFrameError", "args", "computed", "arrayExpression", "slice", "call", "optimiseCall", "optionalCall", "privateNameHandlerLoose", "BASE", "REF", "PROP", "simpleSet", "optionalCallExpression", "transformPrivateNamesUsage", "size", "handler", "memberExpressionToFunctions", "buildPrivateFieldInitLoose", "inheritPropComments", "buildPrivateInstanceFieldInitSpec", "inheritLoc", "expressionStatement", "thisExpression", "buildPrivateStaticFieldInitSpec", "variableDeclaration", "variableDeclarator", "buildPrivateStaticFieldInitSpecOld", "buildPrivateMethodInitLoose", "buildPrivateInstanceMethodInitSpec", "buildPrivateAccessorInitialization", "buildPrivateInstanceMethodInitialization", "buildPublicFieldInitLoose", "isLiteral", "buildPublicFieldInitSpec", "buildPrivateStaticMethodInitLoose", "buildPrivateMethodDeclaration", "generator", "async", "isGetter", "isSetter", "thisArg", "thisRef", "argumentsPath", "thisContextVisitor", "argumentsId", "unshift", "declId", "functionDeclaration", "Identifier", "UnaryExpression", "skipTransparentExprWrapperNodes", "booleanLiteral", "ThisExpression", "needsClassRef", "MetaProperty", "meta", "innerReferencesVisitor", "ReferencedIdentifier", "replaceThisContext", "innerBindingRef", "_state$thisRef", "isNameOrLength", "type", "inheritLeadingComments", "inheritInnerComments", "original", "start", "end", "loc", "buildFieldsInitNodes", "superRef", "setPublicClassFields", "constant<PERSON>uper", "classRefFlags", "injectSuperRef", "staticNodes", "instanceNodes", "lastInstanceNodeReturnsThis", "pureStaticNodes", "classBindingNode", "getSuperRef", "generateUidIdentifierBasedOnNode", "classRefForInnerBinding", "isClassProperty", "assertFieldTransformed", "isStaticBlock", "isInstance", "isPublic", "isField", "ReplaceSupers", "methodPath", "refToPreserve", "getObjectRef", "replace", "replaced", "blockBody", "inheritsComments", "filter", "Boolean", "wrapClass", "leadingComments", "remove", "superClass", "isClassExpression"], "sources": ["../src/fields.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { <PERSON>, Node<PERSON><PERSON>, Visitor, Scope } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type {\n  <PERSON><PERSON>,\n  HandlerState,\n} from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { skipTransparentExprWrapperNodes } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\nimport * as ts from \"./typescript.ts\";\n\ninterface PrivateNameMetadata {\n  id: t.Identifier;\n  static: boolean;\n  method: boolean;\n  getId?: t.Identifier;\n  setId?: t.Identifier;\n  methodId?: t.Identifier;\n  initAdded?: boolean;\n  getterDeclared?: boolean;\n  setterDeclared?: boolean;\n}\n\ntype PrivateNamesMapGeneric<V> = Map<string, V>;\n\ntype PrivateNamesMap = PrivateNamesMapGeneric<PrivateNameMetadata>;\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var newHelpers = (file: File) => {\n    if (!process.env.IS_PUBLISH) {\n      const { comments } = file.ast;\n      // This is needed for the test in\n      // babel-plugin-transform-class-properties/test/fixtures/regression/old-helpers\n      if (comments?.some(c => c.value.includes(\"@force-old-private-helpers\"))) {\n        return false;\n      }\n    }\n    return file.availableHelper(\"classPrivateFieldGet2\");\n  };\n}\n\nexport function buildPrivateNamesMap(\n  className: string,\n  privateFieldsAsSymbolsOrProperties: boolean,\n  props: PropPath[],\n  file: File,\n) {\n  const privateNamesMap: PrivateNamesMap = new Map();\n  let classBrandId: t.Identifier;\n  for (const prop of props) {\n    if (prop.isPrivate()) {\n      const { name } = prop.node.key.id;\n      let update: PrivateNameMetadata = privateNamesMap.get(name);\n      if (!update) {\n        const isMethod = !prop.isProperty();\n        const isStatic = prop.node.static;\n        let initAdded = false;\n        let id: t.Identifier;\n        if (\n          !privateFieldsAsSymbolsOrProperties &&\n          (process.env.BABEL_8_BREAKING || newHelpers(file)) &&\n          isMethod &&\n          !isStatic\n        ) {\n          initAdded = !!classBrandId;\n          classBrandId ??= prop.scope.generateUidIdentifier(\n            `${className}_brand`,\n          );\n          id = classBrandId;\n        } else {\n          id = prop.scope.generateUidIdentifier(name);\n        }\n        update = { id, static: isStatic, method: isMethod, initAdded };\n        privateNamesMap.set(name, update);\n      }\n      if (prop.isClassPrivateMethod()) {\n        if (prop.node.kind === \"get\") {\n          const { body } = prop.node.body;\n          let $: t.Node;\n          if (\n            // If we have\n            //   get #foo() { return _some_fn(this); }\n            // we can use _some_fn directly.\n            body.length === 1 &&\n            t.isReturnStatement(($ = body[0])) &&\n            t.isCallExpression(($ = $.argument)) &&\n            $.arguments.length === 1 &&\n            t.isThisExpression($.arguments[0]) &&\n            t.isIdentifier(($ = $.callee))\n          ) {\n            update.getId = t.cloneNode($);\n            update.getterDeclared = true;\n          } else {\n            update.getId = prop.scope.generateUidIdentifier(`get_${name}`);\n          }\n        } else if (prop.node.kind === \"set\") {\n          const { params } = prop.node;\n          const { body } = prop.node.body;\n          let $: t.Node;\n          if (\n            // If we have\n            //   set #foo(val) { _some_fn(this, val); }\n            // we can use _some_fn directly.\n            body.length === 1 &&\n            t.isExpressionStatement(($ = body[0])) &&\n            t.isCallExpression(($ = $.expression)) &&\n            $.arguments.length === 2 &&\n            t.isThisExpression($.arguments[0]) &&\n            t.isIdentifier($.arguments[1], {\n              name: (params[0] as t.Identifier).name,\n            }) &&\n            t.isIdentifier(($ = $.callee))\n          ) {\n            update.setId = t.cloneNode($);\n            update.setterDeclared = true;\n          } else {\n            update.setId = prop.scope.generateUidIdentifier(`set_${name}`);\n          }\n        } else if (prop.node.kind === \"method\") {\n          update.methodId = prop.scope.generateUidIdentifier(name);\n        }\n      }\n      privateNamesMap.set(name, update);\n    }\n  }\n  return privateNamesMap;\n}\n\nexport function buildPrivateNamesNodes(\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties: boolean,\n  privateFieldsAsSymbols: boolean,\n  state: File,\n) {\n  const initNodes: t.Statement[] = [];\n\n  const injectedIds = new Set<string>();\n\n  for (const [name, value] of privateNamesMap) {\n    // - When the privateFieldsAsProperties assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   secret non-enumerable property. Hence, we also need to generate that\n    //   key (using the classPrivateFieldLooseKey helper).\n    // - When the privateFieldsAsSymbols assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   unique Symbol to define a non-enumerable property.\n    // - In spec mode, only instance fields need a \"private name\" initializer\n    //   because static fields are directly assigned to a variable in the\n    //   buildPrivateStaticFieldInitSpec function.\n    const { static: isStatic, method: isMethod, getId, setId } = value;\n    const isGetterOrSetter = getId || setId;\n    const id = t.cloneNode(value.id);\n\n    let init: t.Expression;\n\n    if (privateFieldsAsProperties) {\n      init = t.callExpression(state.addHelper(\"classPrivateFieldLooseKey\"), [\n        t.stringLiteral(name),\n      ]);\n    } else if (privateFieldsAsSymbols) {\n      init = t.callExpression(t.identifier(\"Symbol\"), [t.stringLiteral(name)]);\n    } else if (!isStatic) {\n      if (injectedIds.has(id.name)) continue;\n      injectedIds.add(id.name);\n\n      init = t.newExpression(\n        t.identifier(\n          isMethod &&\n            (process.env.BABEL_8_BREAKING ||\n              !isGetterOrSetter ||\n              newHelpers(state))\n            ? \"WeakSet\"\n            : \"WeakMap\",\n        ),\n        [],\n      );\n    }\n\n    if (init) {\n      if (!privateFieldsAsSymbols) {\n        annotateAsPure(init);\n      }\n      initNodes.push(template.statement.ast`var ${id} = ${init}`);\n    }\n  }\n\n  return initNodes;\n}\n\nexport interface PrivateNameVisitorState<V> {\n  privateNamesMap: PrivateNamesMapGeneric<V>;\n  redeclared?: string[];\n}\n\n// Traverses the class scope, handling private name references. If an inner\n// class redeclares the same private name, it will hand off traversal to the\n// restricted visitor (which doesn't traverse the inner class's inner scope).\nexport function privateNameVisitorFactory<S, V>(\n  visitor: Visitor<PrivateNameVisitorState<V & PrivateNameMetadata> & S>,\n) {\n  // Traverses the outer portion of a class, without touching the class's inner\n  // scope, for private names.\n  const nestedVisitor = visitors.environmentVisitor({ ...visitor });\n\n  const privateNameVisitor: Visitor<\n    PrivateNameVisitorState<V & PrivateNameMetadata> & S\n  > = {\n    ...visitor,\n\n    Class(path) {\n      const { privateNamesMap } = this;\n      const body = path.get(\"body.body\");\n\n      const visiblePrivateNames = new Map(privateNamesMap);\n      const redeclared = [];\n      for (const prop of body) {\n        if (!prop.isPrivate()) continue;\n        const { name } = prop.node.key.id;\n        visiblePrivateNames.delete(name);\n        redeclared.push(name);\n      }\n\n      // If the class doesn't redeclare any private fields, we can continue with\n      // our overall traversal.\n      if (!redeclared.length) {\n        return;\n      }\n\n      // This class redeclares some private field. We need to process the outer\n      // environment with access to all the outer privates, then we can process\n      // the inner environment with only the still-visible outer privates.\n      path.get(\"body\").traverse(nestedVisitor, {\n        ...this,\n        redeclared,\n      });\n      path.traverse(privateNameVisitor, {\n        ...this,\n        privateNamesMap: visiblePrivateNames,\n      });\n\n      // We'll eventually hit this class node again with the overall Class\n      // Features visitor, which'll process the redeclared privates.\n      path.skipKey(\"body\");\n    },\n  };\n\n  return privateNameVisitor;\n}\n\ninterface PrivateNameState {\n  privateNamesMap: PrivateNamesMap;\n  classRef: t.Identifier;\n  file: File;\n  noDocumentAll: boolean;\n  noUninitializedPrivateFieldAccess: boolean;\n  innerBinding?: t.Identifier;\n}\n\nconst privateNameVisitor = privateNameVisitorFactory<\n  HandlerState<PrivateNameState> & PrivateNameState,\n  PrivateNameMetadata\n>({\n  PrivateName(path, { noDocumentAll }) {\n    const { privateNamesMap, redeclared } = this;\n    const { node, parentPath } = path;\n\n    if (\n      !parentPath.isMemberExpression({ property: node }) &&\n      !parentPath.isOptionalMemberExpression({ property: node })\n    ) {\n      return;\n    }\n    const { name } = node.id;\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared?.includes(name)) return;\n\n    this.handle(parentPath, noDocumentAll);\n  },\n});\n\n// rename all bindings that shadows innerBinding\nfunction unshadow(\n  name: string,\n  scope: Scope,\n  innerBinding: t.Identifier | undefined,\n) {\n  // in some cases, scope.getBinding(name) === undefined\n  // so we check hasBinding to avoid keeping looping\n  // see: https://github.com/babel/babel/pull/13656#discussion_r686030715\n  while (\n    scope?.hasBinding(name) &&\n    !scope.bindingIdentifierEquals(name, innerBinding)\n  ) {\n    scope.rename(name);\n    scope = scope.parent;\n  }\n}\n\nexport function buildCheckInRHS(\n  rhs: t.Expression,\n  file: File,\n  inRHSIsObject?: boolean,\n) {\n  if (inRHSIsObject || !file.availableHelper?.(\"checkInRHS\")) return rhs;\n  return t.callExpression(file.addHelper(\"checkInRHS\"), [rhs]);\n}\n\nconst privateInVisitor = privateNameVisitorFactory<\n  {\n    classRef: t.Identifier;\n    file: File;\n    innerBinding?: t.Identifier;\n    privateFieldsAsProperties: boolean;\n  },\n  PrivateNameMetadata\n>({\n  BinaryExpression(path, { file }) {\n    const { operator, left, right } = path.node;\n    if (operator !== \"in\") return;\n    if (!t.isPrivateName(left)) return;\n\n    const { privateFieldsAsProperties, privateNamesMap, redeclared } = this;\n\n    const { name } = left.id;\n\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared?.includes(name)) return;\n\n    // if there are any local variable shadowing classRef, unshadow it\n    // see #12960\n    unshadow(this.classRef.name, path.scope, this.innerBinding);\n\n    if (privateFieldsAsProperties) {\n      const { id } = privateNamesMap.get(name);\n      path.replaceWith(template.expression.ast`\n        Object.prototype.hasOwnProperty.call(${buildCheckInRHS(\n          right,\n          file,\n        )}, ${t.cloneNode(id)})\n      `);\n      return;\n    }\n\n    const { id, static: isStatic } = privateNamesMap.get(name);\n\n    if (isStatic) {\n      path.replaceWith(\n        template.expression.ast`${buildCheckInRHS(\n          right,\n          file,\n        )} === ${t.cloneNode(this.classRef)}`,\n      );\n      return;\n    }\n\n    path.replaceWith(\n      template.expression.ast`${t.cloneNode(id)}.has(${buildCheckInRHS(\n        right,\n        file,\n      )})`,\n    );\n  },\n});\n\ninterface Receiver {\n  receiver(\n    this: HandlerState<PrivateNameState> & PrivateNameState,\n    member: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ): t.Expression;\n}\n\nfunction readOnlyError(file: File, name: string) {\n  return t.callExpression(file.addHelper(\"readOnlyError\"), [\n    t.stringLiteral(`#${name}`),\n  ]);\n}\n\nfunction writeOnlyError(file: File, name: string) {\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    !file.availableHelper(\"writeOnlyError\")\n  ) {\n    console.warn(\n      `@babel/helpers is outdated, update it to silence this warning.`,\n    );\n    return t.buildUndefinedNode();\n  }\n  return t.callExpression(file.addHelper(\"writeOnlyError\"), [\n    t.stringLiteral(`#${name}`),\n  ]);\n}\n\nfunction buildStaticPrivateFieldAccess<N extends t.Expression>(\n  expr: N,\n  noUninitializedPrivateFieldAccess: boolean,\n) {\n  if (noUninitializedPrivateFieldAccess) return expr;\n  return t.memberExpression(expr, t.identifier(\"_\"));\n}\n\nfunction autoInherits<\n  Member extends { node: t.Node },\n  Result extends t.Node,\n  Fn extends (member: Member, ...args: unknown[]) => Result,\n>(fn: Fn): Fn {\n  return function (this: ThisParameterType<Fn>, member) {\n    return t.inherits(fn.apply(this, arguments as any), member.node);\n  } as Fn;\n}\n\nconst privateNameHandlerSpec: Handler<PrivateNameState & Receiver> & Receiver =\n  {\n    memoise(member, count) {\n      const { scope } = member;\n      const { object } = member.node as { object: t.Expression };\n\n      const memo = scope.maybeGenerateMemoised(object);\n      if (!memo) {\n        return;\n      }\n\n      this.memoiser.set(object, memo, count);\n    },\n\n    receiver(member) {\n      const { object } = member.node as { object: t.Expression };\n\n      if (this.memoiser.has(object)) {\n        return t.cloneNode(this.memoiser.get(object));\n      }\n\n      return t.cloneNode(object);\n    },\n\n    get: autoInherits(function (member) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        innerBinding,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const privateName = member.node.property as t.PrivateName;\n      const { name } = privateName.id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        methodId,\n        getId,\n        setId,\n      } = privateNamesMap.get(name);\n      const isGetterOrSetter = getId || setId;\n\n      const cloneId = (id: t.Identifier) =>\n        t.inherits(t.cloneNode(id), privateName);\n\n      if (isStatic) {\n        // if there are any local variable shadowing classRef, unshadow it\n        // see #12960\n        unshadow(classRef.name, member.scope, innerBinding);\n\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          // NOTE: This package has a peerDependency on @babel/core@^7.0.0, but these\n          // helpers have been introduced in @babel/helpers@7.1.0.\n          const helperName =\n            isMethod && !isGetterOrSetter\n              ? \"classStaticPrivateMethodGet\"\n              : \"classStaticPrivateFieldSpecGet\";\n\n          return t.callExpression(file.addHelper(helperName), [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            cloneId(id),\n          ]);\n        }\n\n        const receiver = this.receiver(member);\n        const skipCheck =\n          t.isIdentifier(receiver) && receiver.name === classRef.name;\n\n        if (!isMethod) {\n          if (skipCheck) {\n            return buildStaticPrivateFieldAccess(\n              cloneId(id),\n              noUninitializedPrivateFieldAccess,\n            );\n          }\n\n          return buildStaticPrivateFieldAccess(\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n              cloneId(id),\n            ]),\n            noUninitializedPrivateFieldAccess,\n          );\n        }\n\n        if (getId) {\n          if (skipCheck) {\n            return t.callExpression(cloneId(getId), [receiver]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateGetter\"), [\n            t.cloneNode(classRef),\n            receiver,\n            cloneId(getId),\n          ]);\n        }\n\n        if (setId) {\n          const err = t.buildUndefinedNode(); // TODO: writeOnlyError(file, name)\n          if (skipCheck) return err;\n          return t.sequenceExpression([\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n            ]),\n            err,\n          ]);\n        }\n\n        if (skipCheck) return cloneId(id);\n        return t.callExpression(file.addHelper(\"assertClassBrand\"), [\n          t.cloneNode(classRef),\n          receiver,\n          cloneId(id),\n        ]);\n      }\n\n      if (isMethod) {\n        if (isGetterOrSetter) {\n          if (!getId) {\n            return t.sequenceExpression([\n              this.receiver(member),\n              writeOnlyError(file, name),\n            ]);\n          }\n          if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n            return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n              this.receiver(member),\n              cloneId(id),\n            ]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateGetter\"), [\n            t.cloneNode(id),\n            this.receiver(member),\n            cloneId(getId),\n          ]);\n        }\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          return t.callExpression(file.addHelper(\"classPrivateMethodGet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n            cloneId(methodId),\n          ]);\n        }\n        return t.callExpression(file.addHelper(\"assertClassBrand\"), [\n          t.cloneNode(id),\n          this.receiver(member),\n          cloneId(methodId),\n        ]);\n      }\n      if (process.env.BABEL_8_BREAKING || newHelpers(file)) {\n        return t.callExpression(file.addHelper(\"classPrivateFieldGet2\"), [\n          cloneId(id),\n          this.receiver(member),\n        ]);\n      }\n\n      return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n        this.receiver(member),\n        cloneId(id),\n      ]);\n    }),\n\n    boundGet(member) {\n      this.memoise(member, 1);\n\n      return t.callExpression(\n        t.memberExpression(this.get(member), t.identifier(\"bind\")),\n        [this.receiver(member)],\n      );\n    },\n\n    set: autoInherits(function (member, value) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const privateName = member.node.property as t.PrivateName;\n      const { name } = privateName.id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n        getId,\n      } = privateNamesMap.get(name);\n      const isGetterOrSetter = getId || setId;\n\n      const cloneId = (id: t.Identifier) =>\n        t.inherits(t.cloneNode(id), privateName);\n\n      if (isStatic) {\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          const helperName =\n            isMethod && !isGetterOrSetter\n              ? \"classStaticPrivateMethodSet\"\n              : \"classStaticPrivateFieldSpecSet\";\n\n          return t.callExpression(file.addHelper(helperName), [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            cloneId(id),\n            value,\n          ]);\n        }\n\n        const receiver = this.receiver(member);\n        const skipCheck =\n          t.isIdentifier(receiver) && receiver.name === classRef.name;\n\n        if (isMethod && !setId) {\n          const err = readOnlyError(file, name);\n          if (skipCheck) return t.sequenceExpression([value, err]);\n          return t.sequenceExpression([\n            value,\n            t.callExpression(file.addHelper(\"assertClassBrand\"), [\n              t.cloneNode(classRef),\n              receiver,\n            ]),\n            readOnlyError(file, name),\n          ]);\n        }\n\n        if (setId) {\n          if (skipCheck) {\n            return t.callExpression(t.cloneNode(setId), [receiver, value]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateSetter\"), [\n            t.cloneNode(classRef),\n            cloneId(setId),\n            receiver,\n            value,\n          ]);\n        }\n        return t.assignmentExpression(\n          \"=\",\n          buildStaticPrivateFieldAccess(\n            cloneId(id),\n            noUninitializedPrivateFieldAccess,\n          ),\n          skipCheck\n            ? value\n            : t.callExpression(file.addHelper(\"assertClassBrand\"), [\n                t.cloneNode(classRef),\n                receiver,\n                value,\n              ]),\n        );\n      }\n      if (isMethod) {\n        if (setId) {\n          if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n            return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n              this.receiver(member),\n              cloneId(id),\n              value,\n            ]);\n          }\n          return t.callExpression(file.addHelper(\"classPrivateSetter\"), [\n            t.cloneNode(id),\n            cloneId(setId),\n            this.receiver(member),\n            value,\n          ]);\n        }\n        return t.sequenceExpression([\n          this.receiver(member),\n          value,\n          readOnlyError(file, name),\n        ]);\n      }\n\n      if (process.env.BABEL_8_BREAKING || newHelpers(file)) {\n        return t.callExpression(file.addHelper(\"classPrivateFieldSet2\"), [\n          cloneId(id),\n          this.receiver(member),\n          value,\n        ]);\n      }\n\n      return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n        this.receiver(member),\n        cloneId(id),\n        value,\n      ]);\n    }),\n\n    destructureSet(member) {\n      const {\n        classRef,\n        privateNamesMap,\n        file,\n        noUninitializedPrivateFieldAccess,\n      } = this;\n      const privateName = member.node.property as t.PrivateName;\n      const { name } = privateName.id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n      } = privateNamesMap.get(name);\n\n      const cloneId = (id: t.Identifier) =>\n        t.inherits(t.cloneNode(id), privateName);\n\n      if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n        if (isStatic) {\n          try {\n            // classStaticPrivateFieldDestructureSet was introduced in 7.13.10\n            // eslint-disable-next-line no-var\n            var helper = file.addHelper(\n              \"classStaticPrivateFieldDestructureSet\",\n            );\n          } catch {\n            throw new Error(\n              \"Babel can not transpile `[C.#p] = [0]` with @babel/helpers < 7.13.10, \\n\" +\n                \"please update @babel/helpers to the latest version.\",\n            );\n          }\n          return t.memberExpression(\n            t.callExpression(helper, [\n              this.receiver(member),\n              t.cloneNode(classRef),\n              cloneId(id),\n            ]),\n            t.identifier(\"value\"),\n          );\n        }\n\n        return t.memberExpression(\n          t.callExpression(file.addHelper(\"classPrivateFieldDestructureSet\"), [\n            this.receiver(member),\n            cloneId(id),\n          ]),\n          t.identifier(\"value\"),\n        );\n      }\n\n      if (isMethod && !setId) {\n        return t.memberExpression(\n          t.sequenceExpression([\n            // @ts-ignore(Babel 7 vs Babel 8) member.node.object is not t.Super\n            member.node.object,\n            readOnlyError(file, name),\n          ]),\n          t.identifier(\"_\"),\n        );\n      }\n\n      if (isStatic && !isMethod) {\n        const getCall = this.get(member);\n        if (\n          !noUninitializedPrivateFieldAccess ||\n          !t.isCallExpression(getCall)\n        ) {\n          return getCall;\n        }\n        const ref = getCall.arguments.pop();\n        getCall.arguments.push(template.expression.ast`(_) => ${ref} = _`);\n        return t.memberExpression(\n          t.callExpression(file.addHelper(\"toSetter\"), [getCall]),\n          t.identifier(\"_\"),\n        );\n      }\n\n      const setCall = this.set(member, t.identifier(\"_\"));\n      if (\n        !t.isCallExpression(setCall) ||\n        !t.isIdentifier(setCall.arguments[setCall.arguments.length - 1], {\n          name: \"_\",\n        })\n      ) {\n        throw member.buildCodeFrameError(\n          \"Internal Babel error while compiling this code. This is a Babel bug. \" +\n            \"Please report it at https://github.com/babel/babel/issues.\",\n        );\n      }\n\n      // someHelper(foo, bar, _) -> someHelper, [foo, bar]\n      // aFn.call(foo, bar, _) -> aFn, [bar], foo\n      let args: t.Expression[];\n      if (\n        t.isMemberExpression(setCall.callee, { computed: false }) &&\n        t.isIdentifier(setCall.callee.property) &&\n        setCall.callee.property.name === \"call\"\n      ) {\n        args = [\n          // @ts-ignore(Babel 7 vs Babel 8) member.node.object is not t.Super\n          setCall.callee.object,\n          t.arrayExpression(\n            // Remove '_'\n            (setCall.arguments as t.Expression[]).slice(1, -1),\n          ),\n          setCall.arguments[0] as t.Expression,\n        ];\n      } else {\n        args = [\n          setCall.callee as t.Expression,\n          t.arrayExpression(\n            // Remove '_'\n            (setCall.arguments as t.Expression[]).slice(0, -1),\n          ),\n        ];\n      }\n\n      return t.memberExpression(\n        t.callExpression(file.addHelper(\"toSetter\"), args),\n        t.identifier(\"_\"),\n      );\n    },\n\n    call(member, args: (t.Expression | t.SpreadElement)[]) {\n      // The first access (the get) should do the memo assignment.\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, false);\n    },\n\n    optionalCall(member, args: (t.Expression | t.SpreadElement)[]) {\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, true);\n    },\n\n    delete() {\n      throw new Error(\n        \"Internal Babel error: deleting private elements is a parsing error.\",\n      );\n    },\n  };\n\nconst privateNameHandlerLoose: Handler<PrivateNameState> = {\n  get(member) {\n    const { privateNamesMap, file } = this;\n    const { object } = member.node;\n    const { name } = (member.node.property as t.PrivateName).id;\n\n    return template.expression`BASE(REF, PROP)[PROP]`({\n      BASE: file.addHelper(\"classPrivateFieldLooseBase\"),\n      REF: t.cloneNode(object),\n      PROP: t.cloneNode(privateNamesMap.get(name).id),\n    });\n  },\n\n  set() {\n    // noop\n    throw new Error(\"private name handler with loose = true don't need set()\");\n  },\n\n  boundGet(member) {\n    return t.callExpression(\n      t.memberExpression(this.get(member), t.identifier(\"bind\")),\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      [t.cloneNode(member.node.object as t.Expression)],\n    );\n  },\n\n  simpleSet(member) {\n    return this.get(member);\n  },\n\n  destructureSet(member) {\n    return this.get(member);\n  },\n\n  call(member, args) {\n    return t.callExpression(this.get(member), args);\n  },\n\n  optionalCall(member, args) {\n    return t.optionalCallExpression(this.get(member), args, true);\n  },\n\n  delete() {\n    throw new Error(\n      \"Internal Babel error: deleting private elements is a parsing error.\",\n    );\n  },\n};\n\nexport function transformPrivateNamesUsage(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  privateNamesMap: PrivateNamesMap,\n  {\n    privateFieldsAsProperties,\n    noUninitializedPrivateFieldAccess,\n    noDocumentAll,\n    innerBinding,\n  }: {\n    privateFieldsAsProperties: boolean;\n    noUninitializedPrivateFieldAccess: boolean;\n    noDocumentAll: boolean;\n    innerBinding: t.Identifier;\n  },\n  state: File,\n) {\n  if (!privateNamesMap.size) return;\n\n  const body = path.get(\"body\");\n  const handler = privateFieldsAsProperties\n    ? privateNameHandlerLoose\n    : privateNameHandlerSpec;\n\n  memberExpressionToFunctions<PrivateNameState>(body, privateNameVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    ...handler,\n    noDocumentAll,\n    noUninitializedPrivateFieldAccess,\n    innerBinding,\n  });\n  body.traverse(privateInVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    privateFieldsAsProperties,\n    innerBinding,\n  });\n}\n\nfunction buildPrivateFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${t.cloneNode(id)}, {\n        // configurable is false by default\n        // enumerable is false by default\n        writable: true,\n        value: ${value}\n      });\n    ` as t.ExpressionStatement,\n    prop,\n  );\n}\n\nfunction buildPrivateInstanceFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${t.cloneNode(id)}.set(${ref}, {\n          // configurable is always false for private elements\n          // enumerable is always false for private elements\n          writable: true,\n          value: ${value},\n        })` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritLoc(\n    inheritPropComments(\n      t.expressionStatement(\n        t.callExpression(helper, [\n          t.thisExpression(),\n          inheritLoc(t.cloneNode(id), prop.node.key),\n          process.env.BABEL_8_BREAKING || newHelpers(state)\n            ? value\n            : template.expression.ast`{ writable: true, value: ${value} }`,\n        ]),\n      ),\n      prop,\n    ),\n    prop.node,\n  );\n}\n\nfunction buildPrivateStaticFieldInitSpec(\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  noUninitializedPrivateFieldAccess: boolean,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n\n  const value = noUninitializedPrivateFieldAccess\n    ? prop.node.value\n    : template.expression.ast`{\n        _: ${prop.node.value || t.buildUndefinedNode()}\n      }`;\n\n  return inheritPropComments(\n    t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(privateName.id), value),\n    ]),\n    prop,\n  );\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var buildPrivateStaticFieldInitSpecOld = function (\n    prop: NodePath<t.ClassPrivateProperty>,\n    privateNamesMap: PrivateNamesMap,\n  ) {\n    const privateName = privateNamesMap.get(prop.node.key.id.name);\n    const { id, getId, setId, initAdded } = privateName;\n    const isGetterOrSetter = getId || setId;\n\n    if (!prop.isProperty() && (initAdded || !isGetterOrSetter)) return;\n\n    if (isGetterOrSetter) {\n      privateNamesMap.set(prop.node.key.id.name, {\n        ...privateName,\n        initAdded: true,\n      });\n\n      return inheritPropComments(\n        template.statement.ast`\n          var ${t.cloneNode(id)} = {\n            // configurable is false by default\n            // enumerable is false by default\n            // writable is false by default\n            get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n            set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n          }\n        `,\n        prop,\n      );\n    }\n\n    const value = prop.node.value || prop.scope.buildUndefinedNode();\n    return inheritPropComments(\n      template.statement.ast`\n        var ${t.cloneNode(id)} = {\n          // configurable is false by default\n          // enumerable is false by default\n          writable: true,\n          value: ${value}\n        };\n      `,\n      prop,\n    );\n  };\n}\n\nfunction buildPrivateMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { methodId, id, getId, setId, initAdded } = privateName;\n  if (initAdded) return;\n\n  if (methodId) {\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          value: ${methodId.name}\n        });\n      ` as t.ExpressionStatement,\n      prop,\n    );\n  }\n  const isGetterOrSetter = getId || setId;\n  if (isGetterOrSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        });\n      ` as t.ExpressionStatement,\n      prop,\n    );\n  }\n}\n\nfunction buildPrivateInstanceMethodInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n\n  if (privateName.initAdded) return;\n\n  if (!process.env.BABEL_8_BREAKING && !newHelpers(state)) {\n    const isGetterOrSetter = privateName.getId || privateName.setId;\n    if (isGetterOrSetter) {\n      return buildPrivateAccessorInitialization(\n        ref,\n        prop,\n        privateNamesMap,\n        state,\n      );\n    }\n  }\n\n  return buildPrivateInstanceMethodInitialization(\n    ref,\n    prop,\n    privateNamesMap,\n    state,\n  );\n}\n\nfunction buildPrivateAccessorInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId } = privateName;\n\n  privateNamesMap.set(prop.node.key.id.name, {\n    ...privateName,\n    initAdded: true,\n  });\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`\n          ${id}.set(${ref}, {\n            get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n            set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n          });\n        ` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritLoc(\n    inheritPropComments(\n      template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)},\n      {\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      },\n    )` as t.ExpressionStatement,\n      prop,\n    ),\n    prop.node,\n  );\n}\n\nfunction buildPrivateInstanceMethodInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id } = privateName;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateMethodInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${id}.add(${ref})` as t.ExpressionStatement,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateMethodInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)}\n    )` as t.ExpressionStatement,\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(ref, key, computed || t.isLiteral(key)),\n        value,\n      ),\n    ),\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n  state: File,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.callExpression(state.addHelper(\"defineProperty\"), [\n        ref,\n        computed || t.isLiteral(key)\n          ? key\n          : t.stringLiteral((key as t.Identifier).name),\n        value,\n      ]),\n    ),\n    prop,\n  );\n}\n\nfunction buildPrivateStaticMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  state: File,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, methodId, getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isGetterOrSetter = getId || setId;\n  if (isGetterOrSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        })\n      `,\n      prop,\n    );\n  }\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${id}, {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        value: ${methodId.name}\n      });\n    `,\n    prop,\n  );\n}\n\nfunction buildPrivateMethodDeclaration(\n  file: File,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsSymbolsOrProperties = false,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const {\n    id,\n    methodId,\n    getId,\n    setId,\n    getterDeclared,\n    setterDeclared,\n    static: isStatic,\n  } = privateName;\n  const { params, body, generator, async } = prop.node;\n  const isGetter = getId && params.length === 0;\n  const isSetter = setId && params.length > 0;\n\n  if ((isGetter && getterDeclared) || (isSetter && setterDeclared)) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n    return null;\n  }\n\n  if (\n    (process.env.BABEL_8_BREAKING || newHelpers(file)) &&\n    (isGetter || isSetter) &&\n    !privateFieldsAsSymbolsOrProperties\n  ) {\n    const scope = prop.get(\"body\").scope;\n    const thisArg = scope.generateUidIdentifier(\"this\");\n    const state: ReplaceThisState = {\n      thisRef: thisArg,\n      argumentsPath: [],\n    };\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    prop.traverse(thisContextVisitor, state);\n    if (state.argumentsPath.length) {\n      const argumentsId = scope.generateUidIdentifier(\"arguments\");\n      scope.push({\n        id: argumentsId,\n        init: template.expression.ast`[].slice.call(arguments, 1)`,\n      });\n      for (const path of state.argumentsPath) {\n        path.replaceWith(t.cloneNode(argumentsId));\n      }\n    }\n\n    params.unshift(t.cloneNode(thisArg));\n  }\n\n  let declId = methodId;\n\n  if (isGetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      getterDeclared: true,\n      initAdded: true,\n    });\n    declId = getId;\n  } else if (isSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      setterDeclared: true,\n      initAdded: true,\n    });\n    declId = setId;\n  } else if (isStatic && !privateFieldsAsSymbolsOrProperties) {\n    declId = id;\n  }\n\n  return inheritPropComments(\n    t.functionDeclaration(\n      t.cloneNode(declId),\n      // @ts-expect-error params for ClassMethod has TSParameterProperty\n      params,\n      body,\n      generator,\n      async,\n    ),\n    prop,\n  );\n}\n\ntype ReplaceThisState = {\n  thisRef: t.Identifier;\n  needsClassRef?: boolean;\n  innerBinding?: t.Identifier | null;\n  argumentsPath?: NodePath<t.Identifier>[];\n};\n\ntype ReplaceInnerBindingReferenceState = ReplaceThisState;\n\nconst thisContextVisitor = visitors.environmentVisitor<ReplaceThisState>({\n  Identifier(path, state) {\n    if (state.argumentsPath && path.node.name === \"arguments\") {\n      state.argumentsPath.push(path);\n    }\n  },\n  UnaryExpression(path) {\n    // Replace `delete this` with `true`\n    const { node } = path;\n    if (node.operator === \"delete\") {\n      const argument = skipTransparentExprWrapperNodes(node.argument);\n      if (t.isThisExpression(argument)) {\n        path.replaceWith(t.booleanLiteral(true));\n      }\n    }\n  },\n  ThisExpression(path, state) {\n    state.needsClassRef = true;\n    path.replaceWith(t.cloneNode(state.thisRef));\n  },\n  MetaProperty(path) {\n    const { node, scope } = path;\n    // if there are `new.target` in static field\n    // we should replace it with `undefined`\n    if (node.meta.name === \"new\" && node.property.name === \"target\") {\n      path.replaceWith(scope.buildUndefinedNode());\n    }\n  },\n});\n\nconst innerReferencesVisitor: Visitor<ReplaceInnerBindingReferenceState> = {\n  ReferencedIdentifier(path, state) {\n    if (\n      path.scope.bindingIdentifierEquals(path.node.name, state.innerBinding)\n    ) {\n      state.needsClassRef = true;\n      path.node.name = state.thisRef.name;\n    }\n  },\n};\n\nfunction replaceThisContext(\n  path: PropPath,\n  ref: t.Identifier,\n  innerBindingRef: t.Identifier | null,\n) {\n  const state: ReplaceThisState = {\n    thisRef: ref,\n    needsClassRef: false,\n    innerBinding: innerBindingRef,\n  };\n  if (!path.isMethod()) {\n    // replace `this` in property initializers and static blocks\n    path.traverse(thisContextVisitor, state);\n  }\n\n  // todo: use innerBinding.referencePaths to avoid full traversal\n  if (\n    innerBindingRef != null &&\n    state.thisRef?.name &&\n    state.thisRef.name !== innerBindingRef.name\n  ) {\n    path.traverse(innerReferencesVisitor, state);\n  }\n\n  return state.needsClassRef;\n}\n\nexport type PropNode =\n  | t.ClassProperty\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.StaticBlock;\nexport type PropPath = NodePath<PropNode>;\n\nfunction isNameOrLength({ key, computed }: t.ClassProperty) {\n  if (key.type === \"Identifier\") {\n    return !computed && (key.name === \"name\" || key.name === \"length\");\n  }\n  if (key.type === \"StringLiteral\") {\n    return key.value === \"name\" || key.value === \"length\";\n  }\n  return false;\n}\n\n/**\n * Inherit comments from class members. This is a reduced version of\n * t.inheritsComments: the trailing comments are not inherited because\n * for most class members except the last one, their trailing comments are\n * the next sibling's leading comments.\n *\n * @template T transformed class member type\n * @param {T} node transformed class member\n * @param {PropPath} prop class member\n * @returns transformed class member type with comments inherited\n */\nfunction inheritPropComments<T extends t.Node>(node: T, prop: PropPath) {\n  t.inheritLeadingComments(node, prop.node);\n  t.inheritInnerComments(node, prop.node);\n  return node;\n}\n\nfunction inheritLoc<T extends t.Node>(node: T, original: t.Node) {\n  node.start = original.start;\n  node.end = original.end;\n  node.loc = original.loc;\n  return node;\n}\n\n/**\n * ClassRefFlag records the requirement of the class binding reference.\n *\n * @enum {number}\n */\nconst enum ClassRefFlag {\n  None,\n  /**\n   * When this flag is enabled, the binding reference can be the class id,\n   * if exists, or the uid identifier generated for class expression. The\n   * reference is safe to be consumed by [[Define]].\n   */\n  ForDefine = 1 << 0,\n  /**\n   * When this flag is enabled, the reference must be a uid, because the outer\n   * class binding can be mutated by user codes.\n   * E.g.\n   * class C { static p = C }; const oldC = C; C = null; oldC.p;\n   * we must memoize class `C` before defining the property `p`.\n   */\n  ForInnerBinding = 1 << 1,\n}\n\nexport function buildFieldsInitNodes(\n  ref: t.Identifier | null,\n  superRef: t.Expression | undefined,\n  props: PropPath[],\n  privateNamesMap: PrivateNamesMap,\n  file: File,\n  setPublicClassFields: boolean,\n  privateFieldsAsSymbolsOrProperties: boolean,\n  noUninitializedPrivateFieldAccess: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier | null,\n) {\n  let classRefFlags = ClassRefFlag.None;\n  let injectSuperRef: t.Identifier;\n  const staticNodes: t.Statement[] = [];\n  const instanceNodes: t.ExpressionStatement[] = [];\n  let lastInstanceNodeReturnsThis = false;\n  // These nodes are pure and can be moved to the closest statement position\n  const pureStaticNodes: t.FunctionDeclaration[] = [];\n  let classBindingNode: t.ExpressionStatement | null = null;\n\n  const getSuperRef = t.isIdentifier(superRef)\n    ? () => superRef\n    : () => {\n        injectSuperRef ??=\n          props[0].scope.generateUidIdentifierBasedOnNode(superRef);\n        return injectSuperRef;\n      };\n\n  const classRefForInnerBinding =\n    ref ??\n    props[0].scope.generateUidIdentifier(innerBindingRef?.name || \"Class\");\n  ref ??= t.cloneNode(innerBindingRef);\n\n  for (const prop of props) {\n    if (prop.isClassProperty()) {\n      ts.assertFieldTransformed(prop);\n    }\n\n    // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n    const isStatic = !t.isStaticBlock?.(prop.node) && prop.node.static;\n    const isInstance = !isStatic;\n    const isPrivate = prop.isPrivate();\n    const isPublic = !isPrivate;\n    const isField = prop.isProperty();\n    const isMethod = !isField;\n    const isStaticBlock = prop.isStaticBlock?.();\n\n    if (isStatic) classRefFlags |= ClassRefFlag.ForDefine;\n\n    if (isStatic || (isMethod && isPrivate) || isStaticBlock) {\n      new ReplaceSupers({\n        methodPath: prop,\n        constantSuper,\n        file: file,\n        refToPreserve: innerBindingRef,\n        getSuperRef,\n        getObjectRef() {\n          classRefFlags |= ClassRefFlag.ForInnerBinding;\n          if (isStatic || isStaticBlock) {\n            return classRefForInnerBinding;\n          } else {\n            return t.memberExpression(\n              classRefForInnerBinding,\n              t.identifier(\"prototype\"),\n            );\n          }\n        },\n      }).replace();\n\n      const replaced = replaceThisContext(\n        prop,\n        classRefForInnerBinding,\n        innerBindingRef,\n      );\n      if (replaced) {\n        classRefFlags |= ClassRefFlag.ForInnerBinding;\n      }\n    }\n\n    lastInstanceNodeReturnsThis = false;\n\n    // TODO(ts): there are so many `ts-expect-error` inside cases since\n    // ts can not infer type from pre-computed values (or a case test)\n    // even change `isStaticBlock` to `t.isStaticBlock(prop)` will not make prop\n    // a `NodePath<t.StaticBlock>`\n    // this maybe a bug for ts\n    switch (true) {\n      case isStaticBlock: {\n        const blockBody = prop.node.body;\n        // We special-case the single expression case to avoid the iife, since\n        // it's common.\n        if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n          staticNodes.push(inheritPropComments(blockBody[0], prop));\n        } else {\n          staticNodes.push(\n            t.inheritsComments(\n              template.statement.ast`(() => { ${blockBody} })()`,\n              prop.node,\n            ),\n          );\n        }\n        break;\n      }\n      case isStatic &&\n        isPrivate &&\n        isField &&\n        privateFieldsAsSymbolsOrProperties:\n        staticNodes.push(\n          buildPrivateFieldInitLoose(t.cloneNode(ref), prop, privateNamesMap),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isField &&\n        !privateFieldsAsSymbolsOrProperties:\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          staticNodes.push(\n            buildPrivateStaticFieldInitSpecOld(prop, privateNamesMap),\n          );\n        } else {\n          staticNodes.push(\n            buildPrivateStaticFieldInitSpec(\n              prop,\n              privateNamesMap,\n              noUninitializedPrivateFieldAccess,\n            ),\n          );\n        }\n        break;\n      case isStatic && isPublic && isField && setPublicClassFields:\n        // Functions always have non-writable .name and .length properties,\n        // so we must always use [[Define]] for them.\n        // It might still be possible to a computed static fields whose resulting\n        // key is \"name\" or \"length\", but the assumption is telling us that it's\n        // not going to happen.\n        if (!isNameOrLength(prop.node)) {\n          staticNodes.push(buildPublicFieldInitLoose(t.cloneNode(ref), prop));\n          break;\n        }\n      // falls through\n      case isStatic && isPublic && isField && !setPublicClassFields:\n        staticNodes.push(\n          buildPublicFieldInitSpec(t.cloneNode(ref), prop, file),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isField &&\n        privateFieldsAsSymbolsOrProperties:\n        instanceNodes.push(\n          buildPrivateFieldInitLoose(t.thisExpression(), prop, privateNamesMap),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isField &&\n        !privateFieldsAsSymbolsOrProperties:\n        instanceNodes.push(\n          buildPrivateInstanceFieldInitSpec(\n            t.thisExpression(),\n            prop,\n            privateNamesMap,\n            file,\n          ),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isMethod &&\n        privateFieldsAsSymbolsOrProperties:\n        instanceNodes.unshift(\n          buildPrivateMethodInitLoose(\n            t.thisExpression(),\n            prop,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isInstance &&\n        isPrivate &&\n        isMethod &&\n        !privateFieldsAsSymbolsOrProperties:\n        instanceNodes.unshift(\n          buildPrivateInstanceMethodInitSpec(\n            t.thisExpression(),\n            prop,\n            privateNamesMap,\n            file,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isMethod &&\n        !privateFieldsAsSymbolsOrProperties:\n        if (!process.env.BABEL_8_BREAKING && !newHelpers(file)) {\n          staticNodes.unshift(\n            // @ts-expect-error checked in switch\n            buildPrivateStaticFieldInitSpecOld(prop, privateNamesMap),\n          );\n        }\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isStatic &&\n        isPrivate &&\n        isMethod &&\n        privateFieldsAsSymbolsOrProperties:\n        staticNodes.unshift(\n          buildPrivateStaticMethodInitLoose(\n            t.cloneNode(ref),\n            prop,\n            file,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            file,\n            prop,\n            privateNamesMap,\n            privateFieldsAsSymbolsOrProperties,\n          ),\n        );\n        break;\n      case isInstance && isPublic && isField && setPublicClassFields:\n        instanceNodes.push(buildPublicFieldInitLoose(t.thisExpression(), prop));\n        break;\n      case isInstance && isPublic && isField && !setPublicClassFields:\n        lastInstanceNodeReturnsThis = true;\n        instanceNodes.push(\n          buildPublicFieldInitSpec(t.thisExpression(), prop, file),\n        );\n        break;\n      default:\n        throw new Error(\"Unreachable.\");\n    }\n  }\n\n  if (classRefFlags & ClassRefFlag.ForInnerBinding && innerBindingRef != null) {\n    classBindingNode = t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.cloneNode(classRefForInnerBinding),\n        t.cloneNode(innerBindingRef),\n      ),\n    );\n  }\n\n  return {\n    staticNodes: staticNodes.filter(Boolean),\n    instanceNodes: instanceNodes.filter(Boolean),\n    lastInstanceNodeReturnsThis,\n    pureStaticNodes: pureStaticNodes.filter(Boolean),\n    classBindingNode,\n    wrapClass(path: NodePath<t.Class>) {\n      for (const prop of props) {\n        // Delete leading comments so that they don't get attached as\n        // trailing comments of the previous sibling.\n        // When transforming props, we explicitly attach their leading\n        // comments to the transformed node with `inheritPropComments`\n        // above.\n        prop.node.leadingComments = null;\n        prop.remove();\n      }\n\n      if (injectSuperRef) {\n        path.scope.push({ id: t.cloneNode(injectSuperRef) });\n        path.set(\n          \"superClass\",\n          t.assignmentExpression(\"=\", injectSuperRef, path.node.superClass),\n        );\n      }\n\n      if (classRefFlags !== ClassRefFlag.None) {\n        if (path.isClassExpression()) {\n          path.scope.push({ id: ref });\n          path.replaceWith(\n            t.assignmentExpression(\"=\", t.cloneNode(ref), path.node),\n          );\n        } else {\n          if (innerBindingRef == null) {\n            // export anonymous class declaration\n            path.node.id = ref;\n          }\n          if (classBindingNode != null) {\n            path.scope.push({ id: classRefForInnerBinding });\n          }\n        }\n      }\n\n      return path;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,oBAAA,GAAAF,OAAA;AACA,IAAAG,kCAAA,GAAAH,OAAA;AAKA,IAAAI,6BAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,wCAAA,GAAAN,OAAA;AAEA,IAAAO,EAAA,GAAAP,OAAA;AAkBmC;EAEjC,IAAIQ,UAAU,GAAIC,IAAU,IAAK;IAAA;IAS/B,OAAOA,IAAI,CAACC,eAAe,CAAC,uBAAuB,CAAC;EACtD,CAAC;AACH;AAEO,SAASC,oBAAoBA,CAClCC,SAAiB,EACjBC,kCAA2C,EAC3CC,KAAiB,EACjBL,IAAU,EACV;EACA,MAAMM,eAAgC,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClD,IAAIC,YAA0B;EAC9B,KAAK,MAAMC,IAAI,IAAIJ,KAAK,EAAE;IACxB,IAAII,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MACpB,MAAM;QAAEC;MAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;MACjC,IAAIC,MAA2B,GAAGT,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;MAC3D,IAAI,CAACI,MAAM,EAAE;QACX,MAAME,QAAQ,GAAG,CAACR,IAAI,CAACS,UAAU,CAAC,CAAC;QACnC,MAAMC,QAAQ,GAAGV,IAAI,CAACG,IAAI,CAACQ,MAAM;QACjC,IAAIC,SAAS,GAAG,KAAK;QACrB,IAAIP,EAAgB;QACpB,IACE,CAACV,kCAAkC,IACFL,UAAU,CAACC,IAAI,CAAC,IACjDiB,QAAQ,IACR,CAACE,QAAQ,EACT;UACAE,SAAS,GAAG,CAAC,CAACb,YAAY;UAC1BA,YAAY,WAAZA,YAAY,GAAZA,YAAY,GAAKC,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAC/C,GAAGpB,SAAS,QACd,CAAC;UACDW,EAAE,GAAGN,YAAY;QACnB,CAAC,MAAM;UACLM,EAAE,GAAGL,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAACZ,IAAI,CAAC;QAC7C;QACAI,MAAM,GAAG;UAAED,EAAE;UAAEM,MAAM,EAAED,QAAQ;UAAEK,MAAM,EAAEP,QAAQ;UAAEI;QAAU,CAAC;QAC9Df,eAAe,CAACmB,GAAG,CAACd,IAAI,EAAEI,MAAM,CAAC;MACnC;MACA,IAAIN,IAAI,CAACiB,oBAAoB,CAAC,CAAC,EAAE;QAC/B,IAAIjB,IAAI,CAACG,IAAI,CAACe,IAAI,KAAK,KAAK,EAAE;UAC5B,MAAM;YAAEC;UAAK,CAAC,GAAGnB,IAAI,CAACG,IAAI,CAACgB,IAAI;UAC/B,IAAIC,CAAS;UACb,IAIED,IAAI,CAACE,MAAM,KAAK,CAAC,IACjBC,WAAC,CAACC,iBAAiB,CAAEH,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAE,CAAC,IAClCG,WAAC,CAACE,gBAAgB,CAAEJ,CAAC,GAAGA,CAAC,CAACK,QAAS,CAAC,IACpCL,CAAC,CAACM,SAAS,CAACL,MAAM,KAAK,CAAC,IACxBC,WAAC,CAACK,gBAAgB,CAACP,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC,IAClCJ,WAAC,CAACM,YAAY,CAAER,CAAC,GAAGA,CAAC,CAACS,MAAO,CAAC,EAC9B;YACAvB,MAAM,CAACwB,KAAK,GAAGR,WAAC,CAACS,SAAS,CAACX,CAAC,CAAC;YAC7Bd,MAAM,CAAC0B,cAAc,GAAG,IAAI;UAC9B,CAAC,MAAM;YACL1B,MAAM,CAACwB,KAAK,GAAG9B,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAAC,OAAOZ,IAAI,EAAE,CAAC;UAChE;QACF,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACe,IAAI,KAAK,KAAK,EAAE;UACnC,MAAM;YAAEe;UAAO,CAAC,GAAGjC,IAAI,CAACG,IAAI;UAC5B,MAAM;YAAEgB;UAAK,CAAC,GAAGnB,IAAI,CAACG,IAAI,CAACgB,IAAI;UAC/B,IAAIC,CAAS;UACb,IAIED,IAAI,CAACE,MAAM,KAAK,CAAC,IACjBC,WAAC,CAACY,qBAAqB,CAAEd,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAE,CAAC,IACtCG,WAAC,CAACE,gBAAgB,CAAEJ,CAAC,GAAGA,CAAC,CAACe,UAAW,CAAC,IACtCf,CAAC,CAACM,SAAS,CAACL,MAAM,KAAK,CAAC,IACxBC,WAAC,CAACK,gBAAgB,CAACP,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC,IAClCJ,WAAC,CAACM,YAAY,CAACR,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,EAAE;YAC7BxB,IAAI,EAAG+B,MAAM,CAAC,CAAC,CAAC,CAAkB/B;UACpC,CAAC,CAAC,IACFoB,WAAC,CAACM,YAAY,CAAER,CAAC,GAAGA,CAAC,CAACS,MAAO,CAAC,EAC9B;YACAvB,MAAM,CAAC8B,KAAK,GAAGd,WAAC,CAACS,SAAS,CAACX,CAAC,CAAC;YAC7Bd,MAAM,CAAC+B,cAAc,GAAG,IAAI;UAC9B,CAAC,MAAM;YACL/B,MAAM,CAAC8B,KAAK,GAAGpC,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAAC,OAAOZ,IAAI,EAAE,CAAC;UAChE;QACF,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACe,IAAI,KAAK,QAAQ,EAAE;UACtCZ,MAAM,CAACgC,QAAQ,GAAGtC,IAAI,CAACa,KAAK,CAACC,qBAAqB,CAACZ,IAAI,CAAC;QAC1D;MACF;MACAL,eAAe,CAACmB,GAAG,CAACd,IAAI,EAAEI,MAAM,CAAC;IACnC;EACF;EACA,OAAOT,eAAe;AACxB;AAEO,SAAS0C,sBAAsBA,CACpC1C,eAAgC,EAChC2C,yBAAkC,EAClCC,sBAA+B,EAC/BC,KAAW,EACX;EACA,MAAMC,SAAwB,GAAG,EAAE;EAEnC,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAS,CAAC;EAErC,KAAK,MAAM,CAAC3C,IAAI,EAAE4C,KAAK,CAAC,IAAIjD,eAAe,EAAE;IAW3C,MAAM;MAAEc,MAAM,EAAED,QAAQ;MAAEK,MAAM,EAAEP,QAAQ;MAAEsB,KAAK;MAAEM;IAAM,CAAC,GAAGU,KAAK;IAClE,MAAMC,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IACvC,MAAM/B,EAAE,GAAGiB,WAAC,CAACS,SAAS,CAACe,KAAK,CAACzC,EAAE,CAAC;IAEhC,IAAI2C,IAAkB;IAEtB,IAAIR,yBAAyB,EAAE;MAC7BQ,IAAI,GAAG1B,WAAC,CAAC2B,cAAc,CAACP,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC,EAAE,CACpE5B,WAAC,CAAC6B,aAAa,CAACjD,IAAI,CAAC,CACtB,CAAC;IACJ,CAAC,MAAM,IAAIuC,sBAAsB,EAAE;MACjCO,IAAI,GAAG1B,WAAC,CAAC2B,cAAc,CAAC3B,WAAC,CAAC8B,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC9B,WAAC,CAAC6B,aAAa,CAACjD,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAI,CAACQ,QAAQ,EAAE;MACpB,IAAIkC,WAAW,CAACS,GAAG,CAAChD,EAAE,CAACH,IAAI,CAAC,EAAE;MAC9B0C,WAAW,CAACU,GAAG,CAACjD,EAAE,CAACH,IAAI,CAAC;MAExB8C,IAAI,GAAG1B,WAAC,CAACiC,aAAa,CACpBjC,WAAC,CAAC8B,UAAU,CACV5C,QAAQ,KAEJ,CAACuC,gBAAgB,IACjBzD,UAAU,CAACoD,KAAK,CAAC,IACjB,SAAS,GACT,SACN,CAAC,EACD,EACF,CAAC;IACH;IAEA,IAAIM,IAAI,EAAE;MACR,IAAI,CAACP,sBAAsB,EAAE;QAC3B,IAAAe,6BAAc,EAACR,IAAI,CAAC;MACtB;MACAL,SAAS,CAACc,IAAI,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAG,OAAOvD,EAAE,MAAM2C,IAAI,EAAE,CAAC;IAC7D;EACF;EAEA,OAAOL,SAAS;AAClB;AAUO,SAASkB,yBAAyBA,CACvCC,OAAsE,EACtE;EAGA,MAAMC,aAAa,GAAGC,kBAAQ,CAACC,kBAAkB,CAAAC,MAAA,CAAAC,MAAA,KAAML,OAAO,CAAE,CAAC;EAEjE,MAAMM,kBAEL,GAAAF,MAAA,CAAAC,MAAA,KACIL,OAAO;IAEVO,KAAKA,CAACC,IAAI,EAAE;MACV,MAAM;QAAEzE;MAAgB,CAAC,GAAG,IAAI;MAChC,MAAMsB,IAAI,GAAGmD,IAAI,CAAC/D,GAAG,CAAC,WAAW,CAAC;MAElC,MAAMgE,mBAAmB,GAAG,IAAIzE,GAAG,CAACD,eAAe,CAAC;MACpD,MAAM2E,UAAU,GAAG,EAAE;MACrB,KAAK,MAAMxE,IAAI,IAAImB,IAAI,EAAE;QACvB,IAAI,CAACnB,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;QACvB,MAAM;UAAEC;QAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;QACjCkE,mBAAmB,CAACE,MAAM,CAACvE,IAAI,CAAC;QAChCsE,UAAU,CAACf,IAAI,CAACvD,IAAI,CAAC;MACvB;MAIA,IAAI,CAACsE,UAAU,CAACnD,MAAM,EAAE;QACtB;MACF;MAKAiD,IAAI,CAAC/D,GAAG,CAAC,MAAM,CAAC,CAACmE,QAAQ,CAACX,aAAa,EAAAG,MAAA,CAAAC,MAAA,KAClC,IAAI;QACPK;MAAU,EACX,CAAC;MACFF,IAAI,CAACI,QAAQ,CAACN,kBAAkB,EAAAF,MAAA,CAAAC,MAAA,KAC3B,IAAI;QACPtE,eAAe,EAAE0E;MAAmB,EACrC,CAAC;MAIFD,IAAI,CAACK,OAAO,CAAC,MAAM,CAAC;IACtB;EAAC,EACF;EAED,OAAOP,kBAAkB;AAC3B;AAWA,MAAMA,kBAAkB,GAAGP,yBAAyB,CAGlD;EACAe,WAAWA,CAACN,IAAI,EAAE;IAAEO;EAAc,CAAC,EAAE;IACnC,MAAM;MAAEhF,eAAe;MAAE2E;IAAW,CAAC,GAAG,IAAI;IAC5C,MAAM;MAAErE,IAAI;MAAE2E;IAAW,CAAC,GAAGR,IAAI;IAEjC,IACE,CAACQ,UAAU,CAACC,kBAAkB,CAAC;MAAEC,QAAQ,EAAE7E;IAAK,CAAC,CAAC,IAClD,CAAC2E,UAAU,CAACG,0BAA0B,CAAC;MAAED,QAAQ,EAAE7E;IAAK,CAAC,CAAC,EAC1D;MACA;IACF;IACA,MAAM;MAAED;IAAK,CAAC,GAAGC,IAAI,CAACE,EAAE;IACxB,IAAI,CAACR,eAAe,CAACwD,GAAG,CAACnD,IAAI,CAAC,EAAE;IAChC,IAAIsE,UAAU,YAAVA,UAAU,CAAEU,QAAQ,CAAChF,IAAI,CAAC,EAAE;IAEhC,IAAI,CAACiF,MAAM,CAACL,UAAU,EAAED,aAAa,CAAC;EACxC;AACF,CAAC,CAAC;AAGF,SAASO,QAAQA,CACflF,IAAY,EACZW,KAAY,EACZwE,YAAsC,EACtC;EAIA,OACE,CAAAC,MAAA,GAAAzE,KAAK,aAALyE,MAAA,CAAOC,UAAU,CAACrF,IAAI,CAAC,IACvB,CAACW,KAAK,CAAC2E,uBAAuB,CAACtF,IAAI,EAAEmF,YAAY,CAAC,EAClD;IAAA,IAAAC,MAAA;IACAzE,KAAK,CAAC4E,MAAM,CAACvF,IAAI,CAAC;IAClBW,KAAK,GAAGA,KAAK,CAAC6E,MAAM;EACtB;AACF;AAEO,SAASC,eAAeA,CAC7BC,GAAiB,EACjBrG,IAAU,EACVsG,aAAuB,EACvB;EACA,IAAIA,aAAa,IAAI,EAACtG,IAAI,CAACC,eAAe,YAApBD,IAAI,CAACC,eAAe,CAAG,YAAY,CAAC,GAAE,OAAOoG,GAAG;EACtE,OAAOtE,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC0C,GAAG,CAAC,CAAC;AAC9D;AAEA,MAAME,gBAAgB,GAAGjC,yBAAyB,CAQhD;EACAkC,gBAAgBA,CAACzB,IAAI,EAAE;IAAE/E;EAAK,CAAC,EAAE;IAC/B,MAAM;MAAEyG,QAAQ;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG5B,IAAI,CAACnE,IAAI;IAC3C,IAAI6F,QAAQ,KAAK,IAAI,EAAE;IACvB,IAAI,CAAC1E,WAAC,CAAC6E,aAAa,CAACF,IAAI,CAAC,EAAE;IAE5B,MAAM;MAAEzD,yBAAyB;MAAE3C,eAAe;MAAE2E;IAAW,CAAC,GAAG,IAAI;IAEvE,MAAM;MAAEtE;IAAK,CAAC,GAAG+F,IAAI,CAAC5F,EAAE;IAExB,IAAI,CAACR,eAAe,CAACwD,GAAG,CAACnD,IAAI,CAAC,EAAE;IAChC,IAAIsE,UAAU,YAAVA,UAAU,CAAEU,QAAQ,CAAChF,IAAI,CAAC,EAAE;IAIhCkF,QAAQ,CAAC,IAAI,CAACgB,QAAQ,CAAClG,IAAI,EAAEoE,IAAI,CAACzD,KAAK,EAAE,IAAI,CAACwE,YAAY,CAAC;IAE3D,IAAI7C,yBAAyB,EAAE;MAC7B,MAAM;QAAEnC;MAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;MACxCoE,IAAI,CAAC+B,WAAW,CAAC3C,cAAQ,CAACvB,UAAU,CAACyB,GAAG;AAC9C,+CAA+C+B,eAAe,CACpDO,KAAK,EACL3G,IACF,CAAC,KAAK+B,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AAC7B,OAAO,CAAC;MACF;IACF;IAEA,MAAM;MAAEA,EAAE;MAAEM,MAAM,EAAED;IAAS,CAAC,GAAGb,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAE1D,IAAIQ,QAAQ,EAAE;MACZ4D,IAAI,CAAC+B,WAAW,CACd3C,cAAQ,CAACvB,UAAU,CAACyB,GAAG,GAAG+B,eAAe,CACvCO,KAAK,EACL3G,IACF,CAAC,QAAQ+B,WAAC,CAACS,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,EACrC,CAAC;MACD;IACF;IAEA9B,IAAI,CAAC+B,WAAW,CACd3C,cAAQ,CAACvB,UAAU,CAACyB,GAAG,GAAGtC,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,QAAQsF,eAAe,CAC9DO,KAAK,EACL3G,IACF,CAAC,GACH,CAAC;EACH;AACF,CAAC,CAAC;AASF,SAAS+G,aAAaA,CAAC/G,IAAU,EAAEW,IAAY,EAAE;EAC/C,OAAOoB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,eAAe,CAAC,EAAE,CACvD5B,WAAC,CAAC6B,aAAa,CAAC,IAAIjD,IAAI,EAAE,CAAC,CAC5B,CAAC;AACJ;AAEA,SAASqG,cAAcA,CAAChH,IAAU,EAAEW,IAAY,EAAE;EAChD,IAEE,CAACX,IAAI,CAACC,eAAe,CAAC,gBAAgB,CAAC,EACvC;IACAgH,OAAO,CAACC,IAAI,CACV,gEACF,CAAC;IACD,OAAOnF,WAAC,CAACoF,kBAAkB,CAAC,CAAC;EAC/B;EACA,OAAOpF,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACxD5B,WAAC,CAAC6B,aAAa,CAAC,IAAIjD,IAAI,EAAE,CAAC,CAC5B,CAAC;AACJ;AAEA,SAASyG,6BAA6BA,CACpCC,IAAO,EACPC,iCAA0C,EAC1C;EACA,IAAIA,iCAAiC,EAAE,OAAOD,IAAI;EAClD,OAAOtF,WAAC,CAACwF,gBAAgB,CAACF,IAAI,EAAEtF,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAAC,CAAC;AACpD;AAEA,SAAS2D,YAAYA,CAInBC,EAAM,EAAM;EACZ,OAAO,UAAuCC,MAAM,EAAE;IACpD,OAAO3F,WAAC,CAAC4F,QAAQ,CAACF,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEzF,SAAgB,CAAC,EAAEuF,MAAM,CAAC9G,IAAI,CAAC;EAClE,CAAC;AACH;AAEA,MAAMiH,sBAAuE,GAC3E;EACEC,OAAOA,CAACJ,MAAM,EAAEK,KAAK,EAAE;IACrB,MAAM;MAAEzG;IAAM,CAAC,GAAGoG,MAAM;IACxB,MAAM;MAAEM;IAAO,CAAC,GAAGN,MAAM,CAAC9G,IAAgC;IAE1D,MAAMqH,IAAI,GAAG3G,KAAK,CAAC4G,qBAAqB,CAACF,MAAM,CAAC;IAChD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAAC1G,GAAG,CAACuG,MAAM,EAAEC,IAAI,EAAEF,KAAK,CAAC;EACxC,CAAC;EAEDK,QAAQA,CAACV,MAAM,EAAE;IACf,MAAM;MAAEM;IAAO,CAAC,GAAGN,MAAM,CAAC9G,IAAgC;IAE1D,IAAI,IAAI,CAACuH,QAAQ,CAACrE,GAAG,CAACkE,MAAM,CAAC,EAAE;MAC7B,OAAOjG,WAAC,CAACS,SAAS,CAAC,IAAI,CAAC2F,QAAQ,CAACnH,GAAG,CAACgH,MAAM,CAAC,CAAC;IAC/C;IAEA,OAAOjG,WAAC,CAACS,SAAS,CAACwF,MAAM,CAAC;EAC5B,CAAC;EAEDhH,GAAG,EAAEwG,YAAY,CAAC,UAAUE,MAAM,EAAE;IAClC,MAAM;MACJb,QAAQ;MACRvG,eAAe;MACfN,IAAI;MACJ8F,YAAY;MACZwB;IACF,CAAC,GAAG,IAAI;IACR,MAAMe,WAAW,GAAGX,MAAM,CAAC9G,IAAI,CAAC6E,QAAyB;IACzD,MAAM;MAAE9E;IAAK,CAAC,GAAG0H,WAAW,CAACvH,EAAE;IAC/B,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBK,MAAM,EAAEP,QAAQ;MAChB8B,QAAQ;MACRR,KAAK;MACLM;IACF,CAAC,GAAGvC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAC7B,MAAM6C,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,MAAMyF,OAAO,GAAIxH,EAAgB,IAC/BiB,WAAC,CAAC4F,QAAQ,CAAC5F,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EAAEuH,WAAW,CAAC;IAE1C,IAAIlH,QAAQ,EAAE;MAGZ0E,QAAQ,CAACgB,QAAQ,CAAClG,IAAI,EAAE+G,MAAM,CAACpG,KAAK,EAAEwE,YAAY,CAAC;MAEnD,IAAqC,CAAC/F,UAAU,CAACC,IAAI,CAAC,EAAE;QAGtD,MAAMuI,UAAU,GACdtH,QAAQ,IAAI,CAACuC,gBAAgB,GACzB,6BAA6B,GAC7B,gCAAgC;QAEtC,OAAOzB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC4E,UAAU,CAAC,EAAE,CAClD,IAAI,CAACH,QAAQ,CAACV,MAAM,CAAC,EACrB3F,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrByB,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC;MACJ;MAEA,MAAMsH,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACV,MAAM,CAAC;MACtC,MAAMc,SAAS,GACbzG,WAAC,CAACM,YAAY,CAAC+F,QAAQ,CAAC,IAAIA,QAAQ,CAACzH,IAAI,KAAKkG,QAAQ,CAAClG,IAAI;MAE7D,IAAI,CAACM,QAAQ,EAAE;QACb,IAAIuH,SAAS,EAAE;UACb,OAAOpB,6BAA6B,CAClCkB,OAAO,CAACxH,EAAE,CAAC,EACXwG,iCACF,CAAC;QACH;QAEA,OAAOF,6BAA6B,CAClCrF,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,EACRE,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC,EACFwG,iCACF,CAAC;MACH;MAEA,IAAI/E,KAAK,EAAE;QACT,IAAIiG,SAAS,EAAE;UACb,OAAOzG,WAAC,CAAC2B,cAAc,CAAC4E,OAAO,CAAC/F,KAAK,CAAC,EAAE,CAAC6F,QAAQ,CAAC,CAAC;QACrD;QACA,OAAOrG,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,EACRE,OAAO,CAAC/F,KAAK,CAAC,CACf,CAAC;MACJ;MAEA,IAAIM,KAAK,EAAE;QACT,MAAM4F,GAAG,GAAG1G,WAAC,CAACoF,kBAAkB,CAAC,CAAC;QAClC,IAAIqB,SAAS,EAAE,OAAOC,GAAG;QACzB,OAAO1G,WAAC,CAAC2G,kBAAkB,CAAC,CAC1B3G,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,CACT,CAAC,EACFK,GAAG,CACJ,CAAC;MACJ;MAEA,IAAID,SAAS,EAAE,OAAOF,OAAO,CAACxH,EAAE,CAAC;MACjC,OAAOiB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAC1D5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,EACRE,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC;IACJ;IAEA,IAAIG,QAAQ,EAAE;MACZ,IAAIuC,gBAAgB,EAAE;QACpB,IAAI,CAACjB,KAAK,EAAE;UACV,OAAOR,WAAC,CAAC2G,kBAAkB,CAAC,CAC1B,IAAI,CAACN,QAAQ,CAACV,MAAM,CAAC,EACrBV,cAAc,CAAChH,IAAI,EAAEW,IAAI,CAAC,CAC3B,CAAC;QACJ;QACA,IAAqC,CAACZ,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD,OAAO+B,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC;QACJ;QACA,OAAOiB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EACf,IAAI,CAACsH,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAAC/F,KAAK,CAAC,CACf,CAAC;MACJ;MACA,IAAqC,CAACxC,UAAU,CAACC,IAAI,CAAC,EAAE;QACtD,OAAO+B,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrB3F,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EACfwH,OAAO,CAACvF,QAAQ,CAAC,CAClB,CAAC;MACJ;MACA,OAAOhB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAC1D5B,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EACf,IAAI,CAACsH,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACvF,QAAQ,CAAC,CAClB,CAAC;IACJ;IACA,IAAoChD,UAAU,CAACC,IAAI,CAAC,EAAE;MACpD,OAAO+B,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D2E,OAAO,CAACxH,EAAE,CAAC,EACX,IAAI,CAACsH,QAAQ,CAACV,MAAM,CAAC,CACtB,CAAC;IACJ;IAEA,OAAO3F,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC;EACJ,CAAC,CAAC;EAEF6H,QAAQA,CAACjB,MAAM,EAAE;IACf,IAAI,CAACI,OAAO,CAACJ,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO3F,WAAC,CAAC2B,cAAc,CACrB3B,WAAC,CAACwF,gBAAgB,CAAC,IAAI,CAACvG,GAAG,CAAC0G,MAAM,CAAC,EAAE3F,WAAC,CAAC8B,UAAU,CAAC,MAAM,CAAC,CAAC,EAC1D,CAAC,IAAI,CAACuE,QAAQ,CAACV,MAAM,CAAC,CACxB,CAAC;EACH,CAAC;EAEDjG,GAAG,EAAE+F,YAAY,CAAC,UAAUE,MAAM,EAAEnE,KAAK,EAAE;IACzC,MAAM;MACJsD,QAAQ;MACRvG,eAAe;MACfN,IAAI;MACJsH;IACF,CAAC,GAAG,IAAI;IACR,MAAMe,WAAW,GAAGX,MAAM,CAAC9G,IAAI,CAAC6E,QAAyB;IACzD,MAAM;MAAE9E;IAAK,CAAC,GAAG0H,WAAW,CAACvH,EAAE;IAC/B,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBK,MAAM,EAAEP,QAAQ;MAChB4B,KAAK;MACLN;IACF,CAAC,GAAGjC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAC7B,MAAM6C,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,MAAMyF,OAAO,GAAIxH,EAAgB,IAC/BiB,WAAC,CAAC4F,QAAQ,CAAC5F,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EAAEuH,WAAW,CAAC;IAE1C,IAAIlH,QAAQ,EAAE;MACZ,IAAqC,CAACpB,UAAU,CAACC,IAAI,CAAC,EAAE;QACtD,MAAMuI,UAAU,GACdtH,QAAQ,IAAI,CAACuC,gBAAgB,GACzB,6BAA6B,GAC7B,gCAAgC;QAEtC,OAAOzB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC4E,UAAU,CAAC,EAAE,CAClD,IAAI,CAACH,QAAQ,CAACV,MAAM,CAAC,EACrB3F,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrByB,OAAO,CAACxH,EAAE,CAAC,EACXyC,KAAK,CACN,CAAC;MACJ;MAEA,MAAM6E,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACV,MAAM,CAAC;MACtC,MAAMc,SAAS,GACbzG,WAAC,CAACM,YAAY,CAAC+F,QAAQ,CAAC,IAAIA,QAAQ,CAACzH,IAAI,KAAKkG,QAAQ,CAAClG,IAAI;MAE7D,IAAIM,QAAQ,IAAI,CAAC4B,KAAK,EAAE;QACtB,MAAM4F,GAAG,GAAG1B,aAAa,CAAC/G,IAAI,EAAEW,IAAI,CAAC;QACrC,IAAI6H,SAAS,EAAE,OAAOzG,WAAC,CAAC2G,kBAAkB,CAAC,CAACnF,KAAK,EAAEkF,GAAG,CAAC,CAAC;QACxD,OAAO1G,WAAC,CAAC2G,kBAAkB,CAAC,CAC1BnF,KAAK,EACLxB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,CACT,CAAC,EACFrB,aAAa,CAAC/G,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC;MACJ;MAEA,IAAIkC,KAAK,EAAE;QACT,IAAI2F,SAAS,EAAE;UACb,OAAOzG,WAAC,CAAC2B,cAAc,CAAC3B,WAAC,CAACS,SAAS,CAACK,KAAK,CAAC,EAAE,CAACuF,QAAQ,EAAE7E,KAAK,CAAC,CAAC;QAChE;QACA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrByB,OAAO,CAACzF,KAAK,CAAC,EACduF,QAAQ,EACR7E,KAAK,CACN,CAAC;MACJ;MACA,OAAOxB,WAAC,CAAC6G,oBAAoB,CAC3B,GAAG,EACHxB,6BAA6B,CAC3BkB,OAAO,CAACxH,EAAE,CAAC,EACXwG,iCACF,CAAC,EACDkB,SAAS,GACLjF,KAAK,GACLxB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,kBAAkB,CAAC,EAAE,CACnD5B,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrBuB,QAAQ,EACR7E,KAAK,CACN,CACP,CAAC;IACH;IACA,IAAItC,QAAQ,EAAE;MACZ,IAAI4B,KAAK,EAAE;QACT,IAAqC,CAAC9C,UAAU,CAACC,IAAI,CAAC,EAAE;UACtD,OAAO+B,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACxH,EAAE,CAAC,EACXyC,KAAK,CACN,CAAC;QACJ;QACA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAC5D5B,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EACfwH,OAAO,CAACzF,KAAK,CAAC,EACd,IAAI,CAACuF,QAAQ,CAACV,MAAM,CAAC,EACrBnE,KAAK,CACN,CAAC;MACJ;MACA,OAAOxB,WAAC,CAAC2G,kBAAkB,CAAC,CAC1B,IAAI,CAACN,QAAQ,CAACV,MAAM,CAAC,EACrBnE,KAAK,EACLwD,aAAa,CAAC/G,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC;IACJ;IAEA,IAAoCZ,UAAU,CAACC,IAAI,CAAC,EAAE;MACpD,OAAO+B,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D2E,OAAO,CAACxH,EAAE,CAAC,EACX,IAAI,CAACsH,QAAQ,CAACV,MAAM,CAAC,EACrBnE,KAAK,CACN,CAAC;IACJ;IAEA,OAAOxB,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACxH,EAAE,CAAC,EACXyC,KAAK,CACN,CAAC;EACJ,CAAC,CAAC;EAEFsF,cAAcA,CAACnB,MAAM,EAAE;IACrB,MAAM;MACJb,QAAQ;MACRvG,eAAe;MACfN,IAAI;MACJsH;IACF,CAAC,GAAG,IAAI;IACR,MAAMe,WAAW,GAAGX,MAAM,CAAC9G,IAAI,CAAC6E,QAAyB;IACzD,MAAM;MAAE9E;IAAK,CAAC,GAAG0H,WAAW,CAACvH,EAAE;IAC/B,MAAM;MACJA,EAAE;MACFM,MAAM,EAAED,QAAQ;MAChBK,MAAM,EAAEP,QAAQ;MAChB4B;IACF,CAAC,GAAGvC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC;IAE7B,MAAM2H,OAAO,GAAIxH,EAAgB,IAC/BiB,WAAC,CAAC4F,QAAQ,CAAC5F,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EAAEuH,WAAW,CAAC;IAE1C,IAAqC,CAACtI,UAAU,CAACC,IAAI,CAAC,EAAE;MACtD,IAAImB,QAAQ,EAAE;QACZ,IAAI;UAGF,IAAI2H,MAAM,GAAG9I,IAAI,CAAC2D,SAAS,CACzB,uCACF,CAAC;QACH,CAAC,CAAC,OAAAoF,OAAA,EAAM;UACN,MAAM,IAAIC,KAAK,CACb,0EAA0E,GACxE,qDACJ,CAAC;QACH;QACA,OAAOjH,WAAC,CAACwF,gBAAgB,CACvBxF,WAAC,CAAC2B,cAAc,CAACoF,MAAM,EAAE,CACvB,IAAI,CAACV,QAAQ,CAACV,MAAM,CAAC,EACrB3F,WAAC,CAACS,SAAS,CAACqE,QAAQ,CAAC,EACrByB,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC,EACFiB,WAAC,CAAC8B,UAAU,CAAC,OAAO,CACtB,CAAC;MACH;MAEA,OAAO9B,WAAC,CAACwF,gBAAgB,CACvBxF,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,iCAAiC,CAAC,EAAE,CAClE,IAAI,CAACyE,QAAQ,CAACV,MAAM,CAAC,EACrBY,OAAO,CAACxH,EAAE,CAAC,CACZ,CAAC,EACFiB,WAAC,CAAC8B,UAAU,CAAC,OAAO,CACtB,CAAC;IACH;IAEA,IAAI5C,QAAQ,IAAI,CAAC4B,KAAK,EAAE;MACtB,OAAOd,WAAC,CAACwF,gBAAgB,CACvBxF,WAAC,CAAC2G,kBAAkB,CAAC,CAEnBhB,MAAM,CAAC9G,IAAI,CAACoH,MAAM,EAClBjB,aAAa,CAAC/G,IAAI,EAAEW,IAAI,CAAC,CAC1B,CAAC,EACFoB,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;IACH;IAEA,IAAI1C,QAAQ,IAAI,CAACF,QAAQ,EAAE;MACzB,MAAMgI,OAAO,GAAG,IAAI,CAACjI,GAAG,CAAC0G,MAAM,CAAC;MAChC,IACE,CAACJ,iCAAiC,IAClC,CAACvF,WAAC,CAACE,gBAAgB,CAACgH,OAAO,CAAC,EAC5B;QACA,OAAOA,OAAO;MAChB;MACA,MAAMC,GAAG,GAAGD,OAAO,CAAC9G,SAAS,CAACgH,GAAG,CAAC,CAAC;MACnCF,OAAO,CAAC9G,SAAS,CAAC+B,IAAI,CAACC,cAAQ,CAACvB,UAAU,CAACyB,GAAG,UAAU6E,GAAG,MAAM,CAAC;MAClE,OAAOnH,WAAC,CAACwF,gBAAgB,CACvBxF,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,UAAU,CAAC,EAAE,CAACsF,OAAO,CAAC,CAAC,EACvDlH,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;IACH;IAEA,MAAMuF,OAAO,GAAG,IAAI,CAAC3H,GAAG,CAACiG,MAAM,EAAE3F,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAAC,CAAC;IACnD,IACE,CAAC9B,WAAC,CAACE,gBAAgB,CAACmH,OAAO,CAAC,IAC5B,CAACrH,WAAC,CAACM,YAAY,CAAC+G,OAAO,CAACjH,SAAS,CAACiH,OAAO,CAACjH,SAAS,CAACL,MAAM,GAAG,CAAC,CAAC,EAAE;MAC/DnB,IAAI,EAAE;IACR,CAAC,CAAC,EACF;MACA,MAAM+G,MAAM,CAAC2B,mBAAmB,CAC9B,uEAAuE,GACrE,4DACJ,CAAC;IACH;IAIA,IAAIC,IAAoB;IACxB,IACEvH,WAAC,CAACyD,kBAAkB,CAAC4D,OAAO,CAAC9G,MAAM,EAAE;MAAEiH,QAAQ,EAAE;IAAM,CAAC,CAAC,IACzDxH,WAAC,CAACM,YAAY,CAAC+G,OAAO,CAAC9G,MAAM,CAACmD,QAAQ,CAAC,IACvC2D,OAAO,CAAC9G,MAAM,CAACmD,QAAQ,CAAC9E,IAAI,KAAK,MAAM,EACvC;MACA2I,IAAI,GAAG,CAELF,OAAO,CAAC9G,MAAM,CAAC0F,MAAM,EACrBjG,WAAC,CAACyH,eAAe,CAEdJ,OAAO,CAACjH,SAAS,CAAoBsH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACnD,CAAC,EACDL,OAAO,CAACjH,SAAS,CAAC,CAAC,CAAC,CACrB;IACH,CAAC,MAAM;MACLmH,IAAI,GAAG,CACLF,OAAO,CAAC9G,MAAM,EACdP,WAAC,CAACyH,eAAe,CAEdJ,OAAO,CAACjH,SAAS,CAAoBsH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACnD,CAAC,CACF;IACH;IAEA,OAAO1H,WAAC,CAACwF,gBAAgB,CACvBxF,WAAC,CAAC2B,cAAc,CAAC1D,IAAI,CAAC2D,SAAS,CAAC,UAAU,CAAC,EAAE2F,IAAI,CAAC,EAClDvH,WAAC,CAAC8B,UAAU,CAAC,GAAG,CAClB,CAAC;EACH,CAAC;EAED6F,IAAIA,CAAChC,MAAM,EAAE4B,IAAwC,EAAE;IAErD,IAAI,CAACxB,OAAO,CAACJ,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAAiC,qCAAY,EAAC,IAAI,CAAC3I,GAAG,CAAC0G,MAAM,CAAC,EAAE,IAAI,CAACU,QAAQ,CAACV,MAAM,CAAC,EAAE4B,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDM,YAAYA,CAAClC,MAAM,EAAE4B,IAAwC,EAAE;IAC7D,IAAI,CAACxB,OAAO,CAACJ,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAAiC,qCAAY,EAAC,IAAI,CAAC3I,GAAG,CAAC0G,MAAM,CAAC,EAAE,IAAI,CAACU,QAAQ,CAACV,MAAM,CAAC,EAAE4B,IAAI,EAAE,IAAI,CAAC;EAC1E,CAAC;EAEDpE,MAAMA,CAAA,EAAG;IACP,MAAM,IAAI8D,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEH,MAAMa,uBAAkD,GAAG;EACzD7I,GAAGA,CAAC0G,MAAM,EAAE;IACV,MAAM;MAAEpH,eAAe;MAAEN;IAAK,CAAC,GAAG,IAAI;IACtC,MAAM;MAAEgI;IAAO,CAAC,GAAGN,MAAM,CAAC9G,IAAI;IAC9B,MAAM;MAAED;IAAK,CAAC,GAAI+G,MAAM,CAAC9G,IAAI,CAAC6E,QAAQ,CAAmB3E,EAAE;IAE3D,OAAOqD,cAAQ,CAACvB,UAAU,uBAAuB,CAAC;MAChDkH,IAAI,EAAE9J,IAAI,CAAC2D,SAAS,CAAC,4BAA4B,CAAC;MAClDoG,GAAG,EAAEhI,WAAC,CAACS,SAAS,CAACwF,MAAM,CAAC;MACxBgC,IAAI,EAAEjI,WAAC,CAACS,SAAS,CAAClC,eAAe,CAACU,GAAG,CAACL,IAAI,CAAC,CAACG,EAAE;IAChD,CAAC,CAAC;EACJ,CAAC;EAEDW,GAAGA,CAAA,EAAG;IAEJ,MAAM,IAAIuH,KAAK,CAAC,yDAAyD,CAAC;EAC5E,CAAC;EAEDL,QAAQA,CAACjB,MAAM,EAAE;IACf,OAAO3F,WAAC,CAAC2B,cAAc,CACrB3B,WAAC,CAACwF,gBAAgB,CAAC,IAAI,CAACvG,GAAG,CAAC0G,MAAM,CAAC,EAAE3F,WAAC,CAAC8B,UAAU,CAAC,MAAM,CAAC,CAAC,EAE1D,CAAC9B,WAAC,CAACS,SAAS,CAACkF,MAAM,CAAC9G,IAAI,CAACoH,MAAsB,CAAC,CAClD,CAAC;EACH,CAAC;EAEDiC,SAASA,CAACvC,MAAM,EAAE;IAChB,OAAO,IAAI,CAAC1G,GAAG,CAAC0G,MAAM,CAAC;EACzB,CAAC;EAEDmB,cAAcA,CAACnB,MAAM,EAAE;IACrB,OAAO,IAAI,CAAC1G,GAAG,CAAC0G,MAAM,CAAC;EACzB,CAAC;EAEDgC,IAAIA,CAAChC,MAAM,EAAE4B,IAAI,EAAE;IACjB,OAAOvH,WAAC,CAAC2B,cAAc,CAAC,IAAI,CAAC1C,GAAG,CAAC0G,MAAM,CAAC,EAAE4B,IAAI,CAAC;EACjD,CAAC;EAEDM,YAAYA,CAAClC,MAAM,EAAE4B,IAAI,EAAE;IACzB,OAAOvH,WAAC,CAACmI,sBAAsB,CAAC,IAAI,CAAClJ,GAAG,CAAC0G,MAAM,CAAC,EAAE4B,IAAI,EAAE,IAAI,CAAC;EAC/D,CAAC;EAEDpE,MAAMA,CAAA,EAAG;IACP,MAAM,IAAI8D,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEM,SAASmB,0BAA0BA,CACxCjB,GAAiB,EACjBnE,IAAuB,EACvBzE,eAAgC,EAChC;EACE2C,yBAAyB;EACzBqE,iCAAiC;EACjChC,aAAa;EACbQ;AAMF,CAAC,EACD3C,KAAW,EACX;EACA,IAAI,CAAC7C,eAAe,CAAC8J,IAAI,EAAE;EAE3B,MAAMxI,IAAI,GAAGmD,IAAI,CAAC/D,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMqJ,OAAO,GAAGpH,yBAAyB,GACrC4G,uBAAuB,GACvBhC,sBAAsB;EAE1B,IAAAyC,0CAA2B,EAAmB1I,IAAI,EAAEiD,kBAAkB,EAAAF,MAAA,CAAAC,MAAA;IACpEtE,eAAe;IACfuG,QAAQ,EAAEqC,GAAG;IACblJ,IAAI,EAAEmD;EAAK,GACRkH,OAAO;IACV/E,aAAa;IACbgC,iCAAiC;IACjCxB;EAAY,EACb,CAAC;EACFlE,IAAI,CAACuD,QAAQ,CAACoB,gBAAgB,EAAE;IAC9BjG,eAAe;IACfuG,QAAQ,EAAEqC,GAAG;IACblJ,IAAI,EAAEmD,KAAK;IACXF,yBAAyB;IACzB6C;EACF,CAAC,CAAC;AACJ;AAEA,SAASyE,0BAA0BA,CACjCrB,GAAiB,EACjBzI,IAAsC,EACtCH,eAAgC,EAChC;EACA,MAAM;IAAEQ;EAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAM4C,KAAK,GAAG9C,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAI9C,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;EAEhE,OAAOqD,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC1B,8BAA8B6E,GAAG,KAAKnH,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AACrD;AACA;AACA;AACA,iBAAiByC,KAAK;AACtB;AACA,KAAK,EACD9C,IACF,CAAC;AACH;AAEA,SAASgK,iCAAiCA,CACxCvB,GAAiB,EACjBzI,IAAsC,EACtCH,eAAgC,EAChC6C,KAAW,EACX;EACA,MAAM;IAAErC;EAAG,CAAC,GAAGR,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAM4C,KAAK,GAAG9C,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAI9C,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;EAE7B;IACjC,IAAI,CAAChE,KAAK,CAAClD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAOuK,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG,GAAGtC,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,QAAQoI,GAAG;AAC3D;AACA;AACA;AACA,mBAAmB3F,KAAK;AACxB,WAAW,EACH9C,IACF,CAAC;IACH;EACF;EAEA,MAAMqI,MAAM,GAAG3F,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAO+G,UAAU,CACfF,mBAAmB,CACjBzI,WAAC,CAAC4I,mBAAmB,CACnB5I,WAAC,CAAC2B,cAAc,CAACoF,MAAM,EAAE,CACvB/G,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAClBF,UAAU,CAAC3I,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC,EAAEL,IAAI,CAACG,IAAI,CAACC,GAAG,CAAC,EACVd,UAAU,CAACoD,KAAK,CAAC,GAC7CI,KAAK,GACLY,cAAQ,CAACvB,UAAU,CAACyB,GAAG,4BAA4Bd,KAAK,IAAI,CACjE,CACH,CAAC,EACD9C,IACF,CAAC,EACDA,IAAI,CAACG,IACP,CAAC;AACH;AAEA,SAASiK,+BAA+BA,CACtCpK,IAAsC,EACtCH,eAAgC,EAChCgH,iCAA0C,EAC1C;EACA,MAAMe,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAE9D,MAAM4C,KAAK,GAAG+D,iCAAiC,GAC3C7G,IAAI,CAACG,IAAI,CAAC2C,KAAK,GACfY,cAAQ,CAACvB,UAAU,CAACyB,GAAG;AAC7B,aAAa5D,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAIxB,WAAC,CAACoF,kBAAkB,CAAC,CAAC;AACtD,QAAQ;EAEN,OAAOqD,mBAAmB,CACxBzI,WAAC,CAAC+I,mBAAmB,CAAC,KAAK,EAAE,CAC3B/I,WAAC,CAACgJ,kBAAkB,CAAChJ,WAAC,CAACS,SAAS,CAAC6F,WAAW,CAACvH,EAAE,CAAC,EAAEyC,KAAK,CAAC,CACzD,CAAC,EACF9C,IACF,CAAC;AACH;AAEmC;EAEjC,IAAIuK,kCAAkC,GAAG,SAAAA,CACvCvK,IAAsC,EACtCH,eAAgC,EAChC;IACA,MAAM+H,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;IAC9D,MAAM;MAAEG,EAAE;MAAEyB,KAAK;MAAEM,KAAK;MAAExB;IAAU,CAAC,GAAGgH,WAAW;IACnD,MAAM7E,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;IAEvC,IAAI,CAACpC,IAAI,CAACS,UAAU,CAAC,CAAC,KAAKG,SAAS,IAAI,CAACmC,gBAAgB,CAAC,EAAE;IAE5D,IAAIA,gBAAgB,EAAE;MACpBlD,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;QACdhH,SAAS,EAAE;MAAI,EAChB,CAAC;MAEF,OAAOmJ,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC9B,gBAAgBtC,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AAC/B;AACA;AACA;AACA,mBAAmByB,KAAK,GAAGA,KAAK,CAAC5B,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACvE,mBAAmBtE,KAAK,GAAGA,KAAK,CAAClC,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACvE;AACA,SAAS,EACD1G,IACF,CAAC;IACH;IAEA,MAAM8C,KAAK,GAAG9C,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAI9C,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;IAChE,OAAOqD,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC5B,cAActC,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AAC7B;AACA;AACA;AACA,mBAAmByC,KAAK;AACxB;AACA,OAAO,EACD9C,IACF,CAAC;EACH,CAAC;AACH;AAEA,SAASwK,2BAA2BA,CAClC/B,GAAiB,EACjBzI,IAAoC,EACpCH,eAAgC,EAChC;EACA,MAAM+H,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEoC,QAAQ;IAAEjC,EAAE;IAAEyB,KAAK;IAAEM,KAAK;IAAExB;EAAU,CAAC,GAAGgH,WAAW;EAC7D,IAAIhH,SAAS,EAAE;EAEf,IAAI0B,QAAQ,EAAE;IACZ,OAAOyH,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC5B,gCAAgC6E,GAAG,KAAKpI,EAAE;AAC1C;AACA;AACA;AACA,mBAAmBiC,QAAQ,CAACpC,IAAI;AAChC;AACA,OAAO,EACDF,IACF,CAAC;EACH;EACA,MAAM+C,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;EACvC,IAAIW,gBAAgB,EAAE;IACpBlD,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;MACdhH,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAOmJ,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC5B,gCAAgC6E,GAAG,KAAKpI,EAAE;AAC1C;AACA;AACA;AACA,iBAAiByB,KAAK,GAAGA,KAAK,CAAC5B,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACrE,iBAAiBtE,KAAK,GAAGA,KAAK,CAAClC,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACrE;AACA,OAAO,EACD1G,IACF,CAAC;EACH;AACF;AAEA,SAASyK,kCAAkCA,CACzChC,GAAiB,EACjBzI,IAAoC,EACpCH,eAAgC,EAChC6C,KAAW,EACX;EACA,MAAMkF,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAE9D,IAAI0H,WAAW,CAAChH,SAAS,EAAE;EAE3B,IAAqC,CAACtB,UAAU,CAACoD,KAAK,CAAC,EAAE;IACvD,MAAMK,gBAAgB,GAAG6E,WAAW,CAAC9F,KAAK,IAAI8F,WAAW,CAACxF,KAAK;IAC/D,IAAIW,gBAAgB,EAAE;MACpB,OAAO2H,kCAAkC,CACvCjC,GAAG,EACHzI,IAAI,EACJH,eAAe,EACf6C,KACF,CAAC;IACH;EACF;EAEA,OAAOiI,wCAAwC,CAC7ClC,GAAG,EACHzI,IAAI,EACJH,eAAe,EACf6C,KACF,CAAC;AACH;AAEA,SAASgI,kCAAkCA,CACzCjC,GAAiB,EACjBzI,IAAoC,EACpCH,eAAgC,EAChC6C,KAAW,EACX;EACA,MAAMkF,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEyB,KAAK;IAAEM;EAAM,CAAC,GAAGwF,WAAW;EAExC/H,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;IACdhH,SAAS,EAAE;EAAI,EAChB,CAAC;EAEiC;IACjC,IAAI,CAAC8B,KAAK,CAAClD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAOuK,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC9B,YAAYvD,EAAE,QAAQoI,GAAG;AACzB,mBAAmB3G,KAAK,GAAGA,KAAK,CAAC5B,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACvE,mBAAmBtE,KAAK,GAAGA,KAAK,CAAClC,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACvE;AACA,SAAS,EACD1G,IACF,CAAC;IACH;EACF;EAEA,MAAMqI,MAAM,GAAG3F,KAAK,CAACQ,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAO+G,UAAU,CACfF,mBAAmB,CACjBrG,cAAQ,CAACC,SAAS,CAACC,GAAG,GAAGyE,MAAM;AACrC,QAAQ/G,WAAC,CAAC6I,cAAc,CAAC,CAAC;AAC1B,QAAQ7I,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AACvB;AACA,eAAeyB,KAAK,GAAGA,KAAK,CAAC5B,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACnE,eAAetE,KAAK,GAAGA,KAAK,CAAClC,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACnE;AACA,MAAM,EACA1G,IACF,CAAC,EACDA,IAAI,CAACG,IACP,CAAC;AACH;AAEA,SAASwK,wCAAwCA,CAC/ClC,GAAiB,EACjBzI,IAAoC,EACpCH,eAAgC,EAChC6C,KAAW,EACX;EACA,MAAMkF,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG;EAAG,CAAC,GAAGuH,WAAW;EAES;IACjC,IAAI,CAAClF,KAAK,CAAClD,eAAe,CAAC,4BAA4B,CAAC,EAAE;MACxD,OAAOuK,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG,GAAGvD,EAAE,QAAQoI,GAAG,GAAG,EACzCzI,IACF,CAAC;IACH;EACF;EAEA,MAAMqI,MAAM,GAAG3F,KAAK,CAACQ,SAAS,CAAC,4BAA4B,CAAC;EAC5D,OAAO6G,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG,GAAGyE,MAAM;AACnC,QAAQ/G,WAAC,CAAC6I,cAAc,CAAC,CAAC;AAC1B,QAAQ7I,WAAC,CAACS,SAAS,CAAC1B,EAAE,CAAC;AACvB,MAAM,EACFL,IACF,CAAC;AACH;AAEA,SAAS4K,yBAAyBA,CAChCnC,GAAiB,EACjBzI,IAA+B,EAC/B;EACA,MAAM;IAAEI,GAAG;IAAE0I;EAAS,CAAC,GAAG9I,IAAI,CAACG,IAAI;EACnC,MAAM2C,KAAK,GAAG9C,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAI9C,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;EAEhE,OAAOqD,mBAAmB,CACxBzI,WAAC,CAAC4I,mBAAmB,CACnB5I,WAAC,CAAC6G,oBAAoB,CACpB,GAAG,EACH7G,WAAC,CAACwF,gBAAgB,CAAC2B,GAAG,EAAErI,GAAG,EAAE0I,QAAQ,IAAIxH,WAAC,CAACuJ,SAAS,CAACzK,GAAG,CAAC,CAAC,EAC1D0C,KACF,CACF,CAAC,EACD9C,IACF,CAAC;AACH;AAEA,SAAS8K,wBAAwBA,CAC/BrC,GAAiB,EACjBzI,IAA+B,EAC/B0C,KAAW,EACX;EACA,MAAM;IAAEtC,GAAG;IAAE0I;EAAS,CAAC,GAAG9I,IAAI,CAACG,IAAI;EACnC,MAAM2C,KAAK,GAAG9C,IAAI,CAACG,IAAI,CAAC2C,KAAK,IAAI9C,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;EAEhE,OAAOqD,mBAAmB,CACxBzI,WAAC,CAAC4I,mBAAmB,CACnB5I,WAAC,CAAC2B,cAAc,CAACP,KAAK,CAACQ,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAClDuF,GAAG,EACHK,QAAQ,IAAIxH,WAAC,CAACuJ,SAAS,CAACzK,GAAG,CAAC,GACxBA,GAAG,GACHkB,WAAC,CAAC6B,aAAa,CAAE/C,GAAG,CAAkBF,IAAI,CAAC,EAC/C4C,KAAK,CACN,CACH,CAAC,EACD9C,IACF,CAAC;AACH;AAEA,SAAS+K,iCAAiCA,CACxCtC,GAAiB,EACjBzI,IAAoC,EACpC0C,KAAW,EACX7C,eAAgC,EAChC;EACA,MAAM+H,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEiC,QAAQ;IAAER,KAAK;IAAEM,KAAK;IAAExB;EAAU,CAAC,GAAGgH,WAAW;EAE7D,IAAIhH,SAAS,EAAE;EAEf,MAAMmC,gBAAgB,GAAGjB,KAAK,IAAIM,KAAK;EACvC,IAAIW,gBAAgB,EAAE;IACpBlD,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;MACdhH,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAOmJ,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC5B,gCAAgC6E,GAAG,KAAKpI,EAAE;AAC1C;AACA;AACA;AACA,iBAAiByB,KAAK,GAAGA,KAAK,CAAC5B,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACrE,iBAAiBtE,KAAK,GAAGA,KAAK,CAAClC,IAAI,GAAGF,IAAI,CAACa,KAAK,CAAC6F,kBAAkB,CAAC,CAAC;AACrE;AACA,OAAO,EACD1G,IACF,CAAC;EACH;EAEA,OAAO+J,mBAAmB,CACxBrG,cAAQ,CAACC,SAAS,CAACC,GAAG;AAC1B,8BAA8B6E,GAAG,KAAKpI,EAAE;AACxC;AACA;AACA;AACA,iBAAiBiC,QAAQ,CAACpC,IAAI;AAC9B;AACA,KAAK,EACDF,IACF,CAAC;AACH;AAEA,SAASgL,6BAA6BA,CACpCzL,IAAU,EACVS,IAAoC,EACpCH,eAAgC,EAChCF,kCAAkC,GAAG,KAAK,EAC1C;EACA,MAAMiI,WAAW,GAAG/H,eAAe,CAACU,GAAG,CAACP,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IACJG,EAAE;IACFiC,QAAQ;IACRR,KAAK;IACLM,KAAK;IACLJ,cAAc;IACdK,cAAc;IACd1B,MAAM,EAAED;EACV,CAAC,GAAGkH,WAAW;EACf,MAAM;IAAE3F,MAAM;IAAEd,IAAI;IAAE8J,SAAS;IAAEC;EAAM,CAAC,GAAGlL,IAAI,CAACG,IAAI;EACpD,MAAMgL,QAAQ,GAAGrJ,KAAK,IAAIG,MAAM,CAACZ,MAAM,KAAK,CAAC;EAC7C,MAAM+J,QAAQ,GAAGhJ,KAAK,IAAIH,MAAM,CAACZ,MAAM,GAAG,CAAC;EAE3C,IAAK8J,QAAQ,IAAInJ,cAAc,IAAMoJ,QAAQ,IAAI/I,cAAe,EAAE;IAChExC,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;MACdhH,SAAS,EAAE;IAAI,EAChB,CAAC;IACF,OAAO,IAAI;EACb;EAEA,IACmCtB,UAAU,CAACC,IAAI,CAAC,KAChD4L,QAAQ,IAAIC,QAAQ,KACrB,CAACzL,kCAAkC,EACnC;IACA,MAAMkB,KAAK,GAAGb,IAAI,CAACO,GAAG,CAAC,MAAM,CAAC,CAACM,KAAK;IACpC,MAAMwK,OAAO,GAAGxK,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;IACnD,MAAM4B,KAAuB,GAAG;MAC9B4I,OAAO,EAAED,OAAO;MAChBE,aAAa,EAAE;IACjB,CAAC;IAEDvL,IAAI,CAAC0E,QAAQ,CAAC8G,kBAAkB,EAAE9I,KAAK,CAAC;IACxC,IAAIA,KAAK,CAAC6I,aAAa,CAAClK,MAAM,EAAE;MAC9B,MAAMoK,WAAW,GAAG5K,KAAK,CAACC,qBAAqB,CAAC,WAAW,CAAC;MAC5DD,KAAK,CAAC4C,IAAI,CAAC;QACTpD,EAAE,EAAEoL,WAAW;QACfzI,IAAI,EAAEU,cAAQ,CAACvB,UAAU,CAACyB,GAAG;MAC/B,CAAC,CAAC;MACF,KAAK,MAAMU,IAAI,IAAI5B,KAAK,CAAC6I,aAAa,EAAE;QACtCjH,IAAI,CAAC+B,WAAW,CAAC/E,WAAC,CAACS,SAAS,CAAC0J,WAAW,CAAC,CAAC;MAC5C;IACF;IAEAxJ,MAAM,CAACyJ,OAAO,CAACpK,WAAC,CAACS,SAAS,CAACsJ,OAAO,CAAC,CAAC;EACtC;EAEA,IAAIM,MAAM,GAAGrJ,QAAQ;EAErB,IAAI6I,QAAQ,EAAE;IACZtL,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;MACd5F,cAAc,EAAE,IAAI;MACpBpB,SAAS,EAAE;IAAI,EAChB,CAAC;IACF+K,MAAM,GAAG7J,KAAK;EAChB,CAAC,MAAM,IAAIsJ,QAAQ,EAAE;IACnBvL,eAAe,CAACmB,GAAG,CAAChB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAAgE,MAAA,CAAAC,MAAA,KACpCyD,WAAW;MACdvF,cAAc,EAAE,IAAI;MACpBzB,SAAS,EAAE;IAAI,EAChB,CAAC;IACF+K,MAAM,GAAGvJ,KAAK;EAChB,CAAC,MAAM,IAAI1B,QAAQ,IAAI,CAACf,kCAAkC,EAAE;IAC1DgM,MAAM,GAAGtL,EAAE;EACb;EAEA,OAAO0J,mBAAmB,CACxBzI,WAAC,CAACsK,mBAAmB,CACnBtK,WAAC,CAACS,SAAS,CAAC4J,MAAM,CAAC,EAEnB1J,MAAM,EACNd,IAAI,EACJ8J,SAAS,EACTC,KACF,CAAC,EACDlL,IACF,CAAC;AACH;AAWA,MAAMwL,kBAAkB,GAAGxH,kBAAQ,CAACC,kBAAkB,CAAmB;EACvE4H,UAAUA,CAACvH,IAAI,EAAE5B,KAAK,EAAE;IACtB,IAAIA,KAAK,CAAC6I,aAAa,IAAIjH,IAAI,CAACnE,IAAI,CAACD,IAAI,KAAK,WAAW,EAAE;MACzDwC,KAAK,CAAC6I,aAAa,CAAC9H,IAAI,CAACa,IAAI,CAAC;IAChC;EACF,CAAC;EACDwH,eAAeA,CAACxH,IAAI,EAAE;IAEpB,MAAM;MAAEnE;IAAK,CAAC,GAAGmE,IAAI;IACrB,IAAInE,IAAI,CAAC6F,QAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAMvE,QAAQ,GAAG,IAAAsK,wEAA+B,EAAC5L,IAAI,CAACsB,QAAQ,CAAC;MAC/D,IAAIH,WAAC,CAACK,gBAAgB,CAACF,QAAQ,CAAC,EAAE;QAChC6C,IAAI,CAAC+B,WAAW,CAAC/E,WAAC,CAAC0K,cAAc,CAAC,IAAI,CAAC,CAAC;MAC1C;IACF;EACF,CAAC;EACDC,cAAcA,CAAC3H,IAAI,EAAE5B,KAAK,EAAE;IAC1BA,KAAK,CAACwJ,aAAa,GAAG,IAAI;IAC1B5H,IAAI,CAAC+B,WAAW,CAAC/E,WAAC,CAACS,SAAS,CAACW,KAAK,CAAC4I,OAAO,CAAC,CAAC;EAC9C,CAAC;EACDa,YAAYA,CAAC7H,IAAI,EAAE;IACjB,MAAM;MAAEnE,IAAI;MAAEU;IAAM,CAAC,GAAGyD,IAAI;IAG5B,IAAInE,IAAI,CAACiM,IAAI,CAAClM,IAAI,KAAK,KAAK,IAAIC,IAAI,CAAC6E,QAAQ,CAAC9E,IAAI,KAAK,QAAQ,EAAE;MAC/DoE,IAAI,CAAC+B,WAAW,CAACxF,KAAK,CAAC6F,kBAAkB,CAAC,CAAC,CAAC;IAC9C;EACF;AACF,CAAC,CAAC;AAEF,MAAM2F,sBAAkE,GAAG;EACzEC,oBAAoBA,CAAChI,IAAI,EAAE5B,KAAK,EAAE;IAChC,IACE4B,IAAI,CAACzD,KAAK,CAAC2E,uBAAuB,CAAClB,IAAI,CAACnE,IAAI,CAACD,IAAI,EAAEwC,KAAK,CAAC2C,YAAY,CAAC,EACtE;MACA3C,KAAK,CAACwJ,aAAa,GAAG,IAAI;MAC1B5H,IAAI,CAACnE,IAAI,CAACD,IAAI,GAAGwC,KAAK,CAAC4I,OAAO,CAACpL,IAAI;IACrC;EACF;AACF,CAAC;AAED,SAASqM,kBAAkBA,CACzBjI,IAAc,EACdmE,GAAiB,EACjB+D,eAAoC,EACpC;EAAA,IAAAC,cAAA;EACA,MAAM/J,KAAuB,GAAG;IAC9B4I,OAAO,EAAE7C,GAAG;IACZyD,aAAa,EAAE,KAAK;IACpB7G,YAAY,EAAEmH;EAChB,CAAC;EACD,IAAI,CAAClI,IAAI,CAAC9D,QAAQ,CAAC,CAAC,EAAE;IAEpB8D,IAAI,CAACI,QAAQ,CAAC8G,kBAAkB,EAAE9I,KAAK,CAAC;EAC1C;EAGA,IACE8J,eAAe,IAAI,IAAI,KAAAC,cAAA,GACvB/J,KAAK,CAAC4I,OAAO,aAAbmB,cAAA,CAAevM,IAAI,IACnBwC,KAAK,CAAC4I,OAAO,CAACpL,IAAI,KAAKsM,eAAe,CAACtM,IAAI,EAC3C;IACAoE,IAAI,CAACI,QAAQ,CAAC2H,sBAAsB,EAAE3J,KAAK,CAAC;EAC9C;EAEA,OAAOA,KAAK,CAACwJ,aAAa;AAC5B;AASA,SAASQ,cAAcA,CAAC;EAAEtM,GAAG;EAAE0I;AAA0B,CAAC,EAAE;EAC1D,IAAI1I,GAAG,CAACuM,IAAI,KAAK,YAAY,EAAE;IAC7B,OAAO,CAAC7D,QAAQ,KAAK1I,GAAG,CAACF,IAAI,KAAK,MAAM,IAAIE,GAAG,CAACF,IAAI,KAAK,QAAQ,CAAC;EACpE;EACA,IAAIE,GAAG,CAACuM,IAAI,KAAK,eAAe,EAAE;IAChC,OAAOvM,GAAG,CAAC0C,KAAK,KAAK,MAAM,IAAI1C,GAAG,CAAC0C,KAAK,KAAK,QAAQ;EACvD;EACA,OAAO,KAAK;AACd;AAaA,SAASiH,mBAAmBA,CAAmB5J,IAAO,EAAEH,IAAc,EAAE;EACtEsB,WAAC,CAACsL,sBAAsB,CAACzM,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACzCmB,WAAC,CAACuL,oBAAoB,CAAC1M,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACvC,OAAOA,IAAI;AACb;AAEA,SAAS8J,UAAUA,CAAmB9J,IAAO,EAAE2M,QAAgB,EAAE;EAC/D3M,IAAI,CAAC4M,KAAK,GAAGD,QAAQ,CAACC,KAAK;EAC3B5M,IAAI,CAAC6M,GAAG,GAAGF,QAAQ,CAACE,GAAG;EACvB7M,IAAI,CAAC8M,GAAG,GAAGH,QAAQ,CAACG,GAAG;EACvB,OAAO9M,IAAI;AACb;AAyBO,SAAS+M,oBAAoBA,CAClCzE,GAAwB,EACxB0E,QAAkC,EAClCvN,KAAiB,EACjBC,eAAgC,EAChCN,IAAU,EACV6N,oBAA6B,EAC7BzN,kCAA2C,EAC3CkH,iCAA0C,EAC1CwG,aAAsB,EACtBb,eAAoC,EACpC;EACA,IAAIc,aAAa,IAAoB;EACrC,IAAIC,cAA4B;EAChC,MAAMC,WAA0B,GAAG,EAAE;EACrC,MAAMC,aAAsC,GAAG,EAAE;EACjD,IAAIC,2BAA2B,GAAG,KAAK;EAEvC,MAAMC,eAAwC,GAAG,EAAE;EACnD,IAAIC,gBAA8C,GAAG,IAAI;EAEzD,MAAMC,WAAW,GAAGvM,WAAC,CAACM,YAAY,CAACuL,QAAQ,CAAC,GACxC,MAAMA,QAAQ,GACd,MAAM;IACJI,cAAc,WAAdA,cAAc,GAAdA,cAAc,GACZ3N,KAAK,CAAC,CAAC,CAAC,CAACiB,KAAK,CAACiN,gCAAgC,CAACX,QAAQ,CAAC;IAC3D,OAAOI,cAAc;EACvB,CAAC;EAEL,MAAMQ,uBAAuB,GAC3BtF,GAAG,WAAHA,GAAG,GACH7I,KAAK,CAAC,CAAC,CAAC,CAACiB,KAAK,CAACC,qBAAqB,CAAC,CAAA0L,eAAe,oBAAfA,eAAe,CAAEtM,IAAI,KAAI,OAAO,CAAC;EACxEuI,GAAG,WAAHA,GAAG,GAAHA,GAAG,GAAKnH,WAAC,CAACS,SAAS,CAACyK,eAAe,CAAC;EAEpC,KAAK,MAAMxM,IAAI,IAAIJ,KAAK,EAAE;IACxB,IAAII,IAAI,CAACgO,eAAe,CAAC,CAAC,EAAE;MAC1B3O,EAAE,CAAC4O,sBAAsB,CAACjO,IAAI,CAAC;IACjC;IAGA,MAAMU,QAAQ,GAAG,EAACY,WAAC,CAAC4M,aAAa,YAAf5M,WAAC,CAAC4M,aAAa,CAAGlO,IAAI,CAACG,IAAI,CAAC,KAAIH,IAAI,CAACG,IAAI,CAACQ,MAAM;IAClE,MAAMwN,UAAU,GAAG,CAACzN,QAAQ;IAC5B,MAAMT,SAAS,GAAGD,IAAI,CAACC,SAAS,CAAC,CAAC;IAClC,MAAMmO,QAAQ,GAAG,CAACnO,SAAS;IAC3B,MAAMoO,OAAO,GAAGrO,IAAI,CAACS,UAAU,CAAC,CAAC;IACjC,MAAMD,QAAQ,GAAG,CAAC6N,OAAO;IACzB,MAAMH,aAAa,GAAGlO,IAAI,CAACkO,aAAa,oBAAlBlO,IAAI,CAACkO,aAAa,CAAG,CAAC;IAE5C,IAAIxN,QAAQ,EAAE4M,aAAa,KAA0B;IAErD,IAAI5M,QAAQ,IAAKF,QAAQ,IAAIP,SAAU,IAAIiO,aAAa,EAAE;MACxD,IAAII,4BAAa,CAAC;QAChBC,UAAU,EAAEvO,IAAI;QAChBqN,aAAa;QACb9N,IAAI,EAAEA,IAAI;QACViP,aAAa,EAAEhC,eAAe;QAC9BqB,WAAW;QACXY,YAAYA,CAAA,EAAG;UACbnB,aAAa,KAAgC;UAC7C,IAAI5M,QAAQ,IAAIwN,aAAa,EAAE;YAC7B,OAAOH,uBAAuB;UAChC,CAAC,MAAM;YACL,OAAOzM,WAAC,CAACwF,gBAAgB,CACvBiH,uBAAuB,EACvBzM,WAAC,CAAC8B,UAAU,CAAC,WAAW,CAC1B,CAAC;UACH;QACF;MACF,CAAC,CAAC,CAACsL,OAAO,CAAC,CAAC;MAEZ,MAAMC,QAAQ,GAAGpC,kBAAkB,CACjCvM,IAAI,EACJ+N,uBAAuB,EACvBvB,eACF,CAAC;MACD,IAAImC,QAAQ,EAAE;QACZrB,aAAa,KAAgC;MAC/C;IACF;IAEAI,2BAA2B,GAAG,KAAK;IAOnC,QAAQ,IAAI;MACV,KAAKQ,aAAa;QAAE;UAClB,MAAMU,SAAS,GAAG5O,IAAI,CAACG,IAAI,CAACgB,IAAI;UAGhC,IAAIyN,SAAS,CAACvN,MAAM,KAAK,CAAC,IAAIC,WAAC,CAACY,qBAAqB,CAAC0M,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YACnEpB,WAAW,CAAC/J,IAAI,CAACsG,mBAAmB,CAAC6E,SAAS,CAAC,CAAC,CAAC,EAAE5O,IAAI,CAAC,CAAC;UAC3D,CAAC,MAAM;YACLwN,WAAW,CAAC/J,IAAI,CACdnC,WAAC,CAACuN,gBAAgB,CAChBnL,cAAQ,CAACC,SAAS,CAACC,GAAG,YAAYgL,SAAS,OAAO,EAClD5O,IAAI,CAACG,IACP,CACF,CAAC;UACH;UACA;QACF;MACA,KAAKO,QAAQ,IACXT,SAAS,IACToO,OAAO,IACP1O,kCAAkC;QAClC6N,WAAW,CAAC/J,IAAI,CACdqG,0BAA0B,CAACxI,WAAC,CAACS,SAAS,CAAC0G,GAAG,CAAC,EAAEzI,IAAI,EAAEH,eAAe,CACpE,CAAC;QACD;MACF,KAAKa,QAAQ,IACXT,SAAS,IACToO,OAAO,IACP,CAAC1O,kCAAkC;QACnC,IAAqC,CAACL,UAAU,CAACC,IAAI,CAAC,EAAE;UACtDiO,WAAW,CAAC/J,IAAI,CACd8G,kCAAkC,CAACvK,IAAI,EAAEH,eAAe,CAC1D,CAAC;QACH,CAAC,MAAM;UACL2N,WAAW,CAAC/J,IAAI,CACd2G,+BAA+B,CAC7BpK,IAAI,EACJH,eAAe,EACfgH,iCACF,CACF,CAAC;QACH;QACA;MACF,KAAKnG,QAAQ,IAAI0N,QAAQ,IAAIC,OAAO,IAAIjB,oBAAoB;QAM1D,IAAI,CAACV,cAAc,CAAC1M,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BqN,WAAW,CAAC/J,IAAI,CAACmH,yBAAyB,CAACtJ,WAAC,CAACS,SAAS,CAAC0G,GAAG,CAAC,EAAEzI,IAAI,CAAC,CAAC;UACnE;QACF;MAEF,KAAKU,QAAQ,IAAI0N,QAAQ,IAAIC,OAAO,IAAI,CAACjB,oBAAoB;QAC3DI,WAAW,CAAC/J,IAAI,CACdqH,wBAAwB,CAACxJ,WAAC,CAACS,SAAS,CAAC0G,GAAG,CAAC,EAAEzI,IAAI,EAAET,IAAI,CACvD,CAAC;QACD;MACF,KAAK4O,UAAU,IACblO,SAAS,IACToO,OAAO,IACP1O,kCAAkC;QAClC8N,aAAa,CAAChK,IAAI,CAChBqG,0BAA0B,CAACxI,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAAEnK,IAAI,EAAEH,eAAe,CACtE,CAAC;QACD;MACF,KAAKsO,UAAU,IACblO,SAAS,IACToO,OAAO,IACP,CAAC1O,kCAAkC;QACnC8N,aAAa,CAAChK,IAAI,CAChBuG,iCAAiC,CAC/B1I,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAClBnK,IAAI,EACJH,eAAe,EACfN,IACF,CACF,CAAC;QACD;MACF,KAAK4O,UAAU,IACblO,SAAS,IACTO,QAAQ,IACRb,kCAAkC;QAClC8N,aAAa,CAAC/B,OAAO,CACnBlB,2BAA2B,CACzBlJ,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAClBnK,IAAI,EACJH,eACF,CACF,CAAC;QACD8N,eAAe,CAAClK,IAAI,CAClBuH,6BAA6B,CAC3BzL,IAAI,EACJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKwO,UAAU,IACblO,SAAS,IACTO,QAAQ,IACR,CAACb,kCAAkC;QACnC8N,aAAa,CAAC/B,OAAO,CACnBjB,kCAAkC,CAChCnJ,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAClBnK,IAAI,EACJH,eAAe,EACfN,IACF,CACF,CAAC;QACDoO,eAAe,CAAClK,IAAI,CAClBuH,6BAA6B,CAC3BzL,IAAI,EACJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKe,QAAQ,IACXT,SAAS,IACTO,QAAQ,IACR,CAACb,kCAAkC;QACnC,IAAqC,CAACL,UAAU,CAACC,IAAI,CAAC,EAAE;UACtDiO,WAAW,CAAC9B,OAAO,CAEjBnB,kCAAkC,CAACvK,IAAI,EAAEH,eAAe,CAC1D,CAAC;QACH;QACA8N,eAAe,CAAClK,IAAI,CAClBuH,6BAA6B,CAC3BzL,IAAI,EACJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKe,QAAQ,IACXT,SAAS,IACTO,QAAQ,IACRb,kCAAkC;QAClC6N,WAAW,CAAC9B,OAAO,CACjBX,iCAAiC,CAC/BzJ,WAAC,CAACS,SAAS,CAAC0G,GAAG,CAAC,EAChBzI,IAAI,EACJT,IAAI,EACJM,eACF,CACF,CAAC;QACD8N,eAAe,CAAClK,IAAI,CAClBuH,6BAA6B,CAC3BzL,IAAI,EACJS,IAAI,EACJH,eAAe,EACfF,kCACF,CACF,CAAC;QACD;MACF,KAAKwO,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAIjB,oBAAoB;QAC5DK,aAAa,CAAChK,IAAI,CAACmH,yBAAyB,CAACtJ,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAAEnK,IAAI,CAAC,CAAC;QACvE;MACF,KAAKmO,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAI,CAACjB,oBAAoB;QAC7DM,2BAA2B,GAAG,IAAI;QAClCD,aAAa,CAAChK,IAAI,CAChBqH,wBAAwB,CAACxJ,WAAC,CAAC6I,cAAc,CAAC,CAAC,EAAEnK,IAAI,EAAET,IAAI,CACzD,CAAC;QACD;MACF;QACE,MAAM,IAAIgJ,KAAK,CAAC,cAAc,CAAC;IACnC;EACF;EAEA,IAAI+E,aAAa,IAA+B,IAAId,eAAe,IAAI,IAAI,EAAE;IAC3EoB,gBAAgB,GAAGtM,WAAC,CAAC4I,mBAAmB,CACtC5I,WAAC,CAAC6G,oBAAoB,CACpB,GAAG,EACH7G,WAAC,CAACS,SAAS,CAACgM,uBAAuB,CAAC,EACpCzM,WAAC,CAACS,SAAS,CAACyK,eAAe,CAC7B,CACF,CAAC;EACH;EAEA,OAAO;IACLgB,WAAW,EAAEA,WAAW,CAACsB,MAAM,CAACC,OAAO,CAAC;IACxCtB,aAAa,EAAEA,aAAa,CAACqB,MAAM,CAACC,OAAO,CAAC;IAC5CrB,2BAA2B;IAC3BC,eAAe,EAAEA,eAAe,CAACmB,MAAM,CAACC,OAAO,CAAC;IAChDnB,gBAAgB;IAChBoB,SAASA,CAAC1K,IAAuB,EAAE;MACjC,KAAK,MAAMtE,IAAI,IAAIJ,KAAK,EAAE;QAMxBI,IAAI,CAACG,IAAI,CAAC8O,eAAe,GAAG,IAAI;QAChCjP,IAAI,CAACkP,MAAM,CAAC,CAAC;MACf;MAEA,IAAI3B,cAAc,EAAE;QAClBjJ,IAAI,CAACzD,KAAK,CAAC4C,IAAI,CAAC;UAAEpD,EAAE,EAAEiB,WAAC,CAACS,SAAS,CAACwL,cAAc;QAAE,CAAC,CAAC;QACpDjJ,IAAI,CAACtD,GAAG,CACN,YAAY,EACZM,WAAC,CAAC6G,oBAAoB,CAAC,GAAG,EAAEoF,cAAc,EAAEjJ,IAAI,CAACnE,IAAI,CAACgP,UAAU,CAClE,CAAC;MACH;MAEA,IAAI7B,aAAa,MAAsB,EAAE;QACvC,IAAIhJ,IAAI,CAAC8K,iBAAiB,CAAC,CAAC,EAAE;UAC5B9K,IAAI,CAACzD,KAAK,CAAC4C,IAAI,CAAC;YAAEpD,EAAE,EAAEoI;UAAI,CAAC,CAAC;UAC5BnE,IAAI,CAAC+B,WAAW,CACd/E,WAAC,CAAC6G,oBAAoB,CAAC,GAAG,EAAE7G,WAAC,CAACS,SAAS,CAAC0G,GAAG,CAAC,EAAEnE,IAAI,CAACnE,IAAI,CACzD,CAAC;QACH,CAAC,MAAM;UACL,IAAIqM,eAAe,IAAI,IAAI,EAAE;YAE3BlI,IAAI,CAACnE,IAAI,CAACE,EAAE,GAAGoI,GAAG;UACpB;UACA,IAAImF,gBAAgB,IAAI,IAAI,EAAE;YAC5BtJ,IAAI,CAACzD,KAAK,CAAC4C,IAAI,CAAC;cAAEpD,EAAE,EAAE0N;YAAwB,CAAC,CAAC;UAClD;QACF;MACF;MAEA,OAAOzJ,IAAI;IACb;EACF,CAAC;AACH", "ignoreList": []}