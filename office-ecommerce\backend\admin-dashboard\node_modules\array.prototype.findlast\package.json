{"name": "array.prototype.findlast", "version": "1.2.5", "description": "An ESnext spec-compliant `Array.prototype.findLast` shim/polyfill/replacement that works as far down as ES3.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "license": "MIT", "bugs": {"url": "https://github.com/es-shims/Array.prototype.findLast/issues"}, "homepage": "https://github.com/es-shims/Array.prototype.findLast#readme", "main": "index.js", "exports": {".": "./index.js", "./auto": "./auto.js", "./polyfill": "./polyfill.js", "./implementation": "./implementation.js", "./shim": "./shim.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint && evalmd README.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/Array.prototype.findLast.git"}, "keywords": ["Array.prototype.findLast", "find", "findLast", "array", "ESnext", "shim", "polyfill", "last", "es-shim API"], "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "devDependencies": {"@es-shims/api": "^2.4.2", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "globalthis": "^1.0.3", "has-strict-mode": "^1.0.1", "hasown": "^2.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5"}, "testling": {"files": ["test/index.js", "test/shimmed.js"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}